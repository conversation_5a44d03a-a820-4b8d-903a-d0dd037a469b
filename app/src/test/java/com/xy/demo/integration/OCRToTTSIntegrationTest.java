package com.xy.demo.integration;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.xy.demo.ocr.GoogleOCR;
import com.xy.demo.ocr.OCRCallback;
import com.xy.demo.tts.AzureTextSynthesis;

import java.lang.reflect.Method;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * OCR到TTS完整流程集成測試
 * 測試從圖像OCR識別到語音合成的完整流程
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class OCRToTTSIntegrationTest {

    private GoogleOCR googleOCR;
    private AzureTextSynthesis azureTTS;
    
    @Mock
    private OCRCallback mockOCRCallback;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        googleOCR = new GoogleOCR();
        azureTTS = new AzureTextSynthesis();
    }

    /**
     * 測試完整的OCR→TTS流程：新架構下的分離式處理
     */
    @Test
    public void testCompleteOCRToTTSFlow() throws Exception {
        // 模擬OCR識別結果（混合中英文）
        String ocrResult = "這是測試文本123 with English words\n第二行包含數字456和more text。";

        // 1. 測試GoogleOCR的UI顯示優化（單一職責）
        Method normalizeForDisplay = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeForDisplay.setAccessible(true);

        String uiOptimizedText = (String) normalizeForDisplay.invoke(googleOCR, ocrResult);

        assertNotNull("UI優化結果不應為null", uiOptimizedText);
        assertTrue("應該包含原始內容", uiOptimizedText.contains("這是測試文本"));
        assertTrue("應該包含英文內容", uiOptimizedText.contains("English"));

        // 2. 測試AzureTextSynthesis的TTS預處理（接受UI優化文本）
        String ttsOptimizedText = azureTTS.preprocessTextForTTS(uiOptimizedText);

        assertNotNull("TTS預處理結果不應為null", ttsOptimizedText);
        assertTrue("應該包含原始內容", ttsOptimizedText.contains("這是測試文本"));
        assertTrue("應該包含英文內容", ttsOptimizedText.contains("English"));

        // 3. 驗證UI和TTS文本的一致性（>95%相似度）
        double similarity = calculateTextSimilarity(uiOptimizedText, ttsOptimizedText);
        assertTrue("UI和TTS文本相似度應該>95%，實際：" + similarity + "%", similarity > 95.0);

        // 4. 測試TTS的句子級別處理
        Method processBySentences = AzureTextSynthesis.class.getDeclaredMethod("processTextBySentences", String.class, float.class);
        processBySentences.setAccessible(true);

        String ssmlResult = (String) processBySentences.invoke(azureTTS, ttsOptimizedText, 1.0f);

        assertNotNull("SSML結果不應為null", ssmlResult);
        assertTrue("應該包含SSML結構", ssmlResult.contains("<speak"));
        assertTrue("應該包含多語言voice", ssmlResult.contains("JennyMultilingualNeural"));
        assertTrue("應該包含語言標籤", ssmlResult.contains("<lang"));

        // 5. 驗證語言切換和停頓處理
        assertTrue("應該包含中英文切換停頓", ssmlResult.contains("break time="));
        assertTrue("應該包含句子間停頓", ssmlResult.contains("600ms"));

        // 6. 驗證韻律增強
        if (ttsOptimizedText.contains("123") || ttsOptimizedText.contains("456")) {
            assertTrue("數字應該有韻律處理", ssmlResult.contains("<prosody") || ssmlResult.contains("rate="));
        }
        
        System.out.println("OCR結果: " + ocrResult);
        System.out.println("UI優化: " + uiOptimizedText);
        System.out.println("TTS優化: " + ttsOptimizedText);
        System.out.println("文本相似度: " + similarity + "%");
        System.out.println("SSML長度: " + ssmlResult.length());
    }

    /**
     * 測試不同語言比例的處理：新架構下的分離式處理
     */
    @Test
    public void testDifferentLanguageRatios() throws Exception {
        Method normalizeForDisplay = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeForDisplay.setAccessible(true);

        Method processBySentences = AzureTextSynthesis.class.getDeclaredMethod("processTextBySentences", String.class, float.class);
        processBySentences.setAccessible(true);

        // 測試案例1：英文主導（約60%英文）
        String englishDominated = "This is a long English sentence with some 中文 words mixed in.";
        String englishUIOptimized = (String) normalizeForDisplay.invoke(googleOCR, englishDominated);
        String englishTTSOptimized = azureTTS.preprocessTextForTTS(englishUIOptimized);
        String englishSSML = (String) processBySentences.invoke(azureTTS, englishTTSOptimized, 1.0f);

        assertNotNull("英文主導SSML不應為null", englishSSML);
        assertTrue("應該包含英文語言標籤", englishSSML.contains("en-US"));

        // 驗證UI和TTS文本一致性
        double englishSimilarity = calculateTextSimilarity(englishUIOptimized, englishTTSOptimized);
        assertTrue("英文主導文本相似度應該>95%", englishSimilarity > 95.0);

        // 測試案例2：中文主導（約70%中文）
        String chineseDominated = "這是一個很長的中文句子，包含一些English單詞在其中。";
        String chineseUIOptimized = (String) normalizeForDisplay.invoke(googleOCR, chineseDominated);
        String chineseTTSOptimized = azureTTS.preprocessTextForTTS(chineseUIOptimized);
        String chineseSSML = (String) processBySentences.invoke(azureTTS, chineseTTSOptimized, 1.0f);

        assertNotNull("中文主導SSML不應為null", chineseSSML);
        assertTrue("應該包含中文語言標籤", chineseSSML.contains("zh-") || chineseSSML.contains("zh-CN") || chineseSSML.contains("zh-HK"));

        // 驗證UI和TTS文本一致性
        double chineseSimilarity = calculateTextSimilarity(chineseUIOptimized, chineseTTSOptimized);
        assertTrue("中文主導文本相似度應該>95%", chineseSimilarity > 95.0);

        // 測試案例3：平衡情況（約50-50）
        String balanced = "Half English text here. 一半中文文本在這裡。";
        String balancedUIOptimized = (String) normalizeForDisplay.invoke(googleOCR, balanced);
        String balancedTTSOptimized = azureTTS.preprocessTextForTTS(balancedUIOptimized);
        String balancedSSML = (String) processBySentences.invoke(azureTTS, balancedTTSOptimized, 1.0f);
        
        assertNotNull("平衡SSML不應為null", balancedSSML);
        assertTrue("應該包含多種語言標籤", balancedSSML.contains("<lang"));

        // 驗證UI和TTS文本一致性
        double balancedSimilarity = calculateTextSimilarity(balancedUIOptimized, balancedTTSOptimized);
        assertTrue("平衡文本相似度應該>95%", balancedSimilarity > 95.0);
    }

    /**
     * 測試數字處理的智能語言選擇
     */
    @Test
    public void testIntelligentNumberProcessing() throws Exception {
        Method normalizeForTTS = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.class);
        normalizeForTTS.setAccessible(true);
        
        Method processBySentences = AzureTextSynthesis.class.getDeclaredMethod("processTextBySentences", String.class, float.class);
        processBySentences.setAccessible(true);
        
        // 測試案例1：英文環境中的數字
        String englishWithNumbers = "The price is 123 dollars and 456 cents in total.";
        String englishOptimized = (String) normalizeForTTS.invoke(googleOCR, englishWithNumbers, GoogleOCR.OutputType.TTS);
        String englishSSML = (String) processBySentences.invoke(azureTTS, englishOptimized, 1.0f);
        
        assertNotNull("英文數字SSML不應為null", englishSSML);
        // 在英文主導環境中，數字應該用英文發音
        
        // 測試案例2：中文環境中的數字
        String chineseWithNumbers = "價格是123元，總共456塊錢。";
        String chineseOptimized = (String) normalizeForTTS.invoke(googleOCR, chineseWithNumbers, GoogleOCR.OutputType.TTS);
        String chineseSSML = (String) processBySentences.invoke(azureTTS, chineseOptimized, 1.0f);
        
        assertNotNull("中文數字SSML不應為null", chineseSSML);
        // 在中文主導環境中，數字應該用中文發音
        
        // 測試案例3：混合環境中的數字（測試55%閾值）
        String mixedWithNumbers = "產品編號123 product code 456 序列號789";
        String mixedOptimized = (String) normalizeForTTS.invoke(googleOCR, mixedWithNumbers, GoogleOCR.OutputType.TTS);
        String mixedSSML = (String) processBySentences.invoke(azureTTS, mixedOptimized, 1.0f);
        
        assertNotNull("混合數字SSML不應為null", mixedSSML);
        assertTrue("應該包含數字處理", mixedSSML.contains("123") || mixedSSML.contains("456") || mixedSSML.contains("789"));
    }

    /**
     * 測試特殊字符和標點符號的處理
     */
    @Test
    public void testSpecialCharactersAndPunctuation() throws Exception {
        Method normalizeForTTS = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.TTS);
        normalizeForTTS.setAccessible(true);
        
        Method processBySentences = AzureTextSynthesis.class.getDeclaredMethod("processTextBySentences", String.class, float.class);
        processBySentences.setAccessible(true);
        
        // 測試特殊字符轉換
        String specialChars = "測試&符號@處理#效果...省略號！感嘆號？疑問號";
        String optimized = (String) normalizeForTTS.invoke(googleOCR, specialChars, GoogleOCR.OutputType.TTS);
        String ssml = (String) processBySentences.invoke(azureTTS, optimized, 1.0f);
        
        assertNotNull("特殊字符SSML不應為null", ssml);
        
        // 驗證TTS優化：特殊字符轉換
        assertTrue("&應該被轉換為'和'", optimized.contains("和"));
        assertTrue("@應該被轉換為'at'", optimized.contains("at"));
        assertTrue("#應該被轉換為'號'", optimized.contains("號"));
        assertFalse("省略號應該被轉換", optimized.contains("..."));
        
        // 驗證韻律增強：疑問句和感嘆句
        assertTrue("疑問句應該有韻律處理", ssml.contains("<prosody") || ssml.contains("pitch="));
    }

    /**
     * 測試性能基準
     */
    @Test
    public void testPerformanceBenchmark() throws Exception {
        Method normalizeForTTS = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.class);
        normalizeForTTS.setAccessible(true);
        
        Method processBySentences = AzureTextSynthesis.class.getDeclaredMethod("processTextBySentences", String.class, float.class);
        processBySentences.setAccessible(true);
        
        // 創建較大的測試文本
        StringBuilder largeText = new StringBuilder();
        for (int i = 0; i < 20; i++) {
            largeText.append("這是第").append(i + 1).append("行中文文本，包含English words和數字").append(i * 100).append("。\n");
        }
        
        String testText = largeText.toString();
        
        // 測試OCR優化性能
        long ocrStart = System.currentTimeMillis();
        String optimized = (String) normalizeForTTS.invoke(googleOCR, testText, GoogleOCR.OutputType.TTS);
        long ocrEnd = System.currentTimeMillis();
        
        // 測試TTS處理性能
        long ttsStart = System.currentTimeMillis();
        String ssml = (String) processBySentences.invoke(azureTTS, optimized, 1.0f);
        long ttsEnd = System.currentTimeMillis();
        
        assertNotNull("大文本OCR優化結果不應為null", optimized);
        assertNotNull("大文本TTS結果不應為null", ssml);
        
        long ocrTime = ocrEnd - ocrStart;
        long ttsTime = ttsEnd - ttsStart;
        long totalTime = ocrTime + ttsTime;
        
        System.out.println("性能測試結果:");
        System.out.println("  文本長度: " + testText.length() + " 字符");
        System.out.println("  OCR優化時間: " + ocrTime + "ms");
        System.out.println("  TTS處理時間: " + ttsTime + "ms");
        System.out.println("  總處理時間: " + totalTime + "ms");
        System.out.println("  SSML長度: " + ssml.length() + " 字符");
        
        // 性能要求：處理20行文本應該在3秒內完成
        assertTrue("總處理時間應該在3秒內: " + totalTime + "ms", totalTime < 3000);
        
        // 驗證結果質量
        assertTrue("優化後文本應該包含原始內容", optimized.length() > 0);
        assertTrue("SSML應該包含完整結構", ssml.contains("<speak") && ssml.contains("</speak>"));
        assertTrue("SSML應該包含多語言處理", ssml.contains("<lang"));
    }

    /**
     * 測試錯誤恢復和邊界情況
     */
    @Test
    public void testErrorRecoveryAndEdgeCases() throws Exception {
        Method normalizeForTTS = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.class);
        normalizeForTTS.setAccessible(true);
        
        Method processBySentences = AzureTextSynthesis.class.getDeclaredMethod("processTextBySentences", String.class, float.class);
        processBySentences.setAccessible(true);
        
        // 測試極端情況
        String[] edgeCases = {
            "",                           // 空字符串
            "   ",                        // 只有空格
            "\n\n\n",                     // 只有換行符
            "A",                          // 單個字符
            "中",                         // 單個中文字符
            "123",                        // 只有數字
            "!@#$%^&*()",                // 只有特殊字符
            "測試\0null字符",              // 包含null字符
            "超長".repeat(1000)           // 超長重複文本
        };
        
        for (String edgeCase : edgeCases) {
            try {
                String optimized = (String) normalizeForTTS.invoke(googleOCR, edgeCase, GoogleOCR.OutputType.TTS);
                String ssml = (String) processBySentences.invoke(azureTTS, optimized, 1.0f);
                
                // 所有邊界情況都應該有合理的處理結果
                assertNotNull("邊界情況 '" + edgeCase + "' 的OCR結果不應為null", optimized);
                assertNotNull("邊界情況 '" + edgeCase + "' 的TTS結果不應為null", ssml);
                
                if (!edgeCase.trim().isEmpty()) {
                    assertTrue("非空邊界情況應該產生有效SSML", ssml.contains("<speak"));
                }
                
            } catch (Exception e) {
                fail("邊界情況 '" + edgeCase + "' 處理失敗: " + e.getMessage());
            }
        }
    }

    /**
     * 計算兩個文本的相似度百分比
     * 使用簡單的字符級別比較來評估一致性
     */
    private double calculateTextSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null) {
            return 0.0;
        }

        if (text1.equals(text2)) {
            return 100.0;
        }

        // 計算最長公共子序列長度
        int maxLength = Math.max(text1.length(), text2.length());
        if (maxLength == 0) {
            return 100.0;
        }

        // 簡單的字符匹配計算
        int matches = 0;
        int minLength = Math.min(text1.length(), text2.length());

        for (int i = 0; i < minLength; i++) {
            if (text1.charAt(i) == text2.charAt(i)) {
                matches++;
            }
        }

        // 計算相似度百分比
        double similarity = (double) matches / maxLength * 100.0;
        return similarity;
    }
}
