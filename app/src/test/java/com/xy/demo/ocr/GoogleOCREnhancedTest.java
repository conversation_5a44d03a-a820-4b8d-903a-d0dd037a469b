package com.xy.demo.ocr;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Method;

/**
 * 增強版GoogleOCR測試 - 測試UI顯示優化功能
 * 專注於測試單一職責的UI文本處理效果
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class GoogleOCREnhancedTest {

    private GoogleOCR googleOCR;
    
    @Mock
    private OCRCallback mockCallback;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        googleOCR = new GoogleOCR();
    }

    /**
     * 測試UI顯示模式的文本優化
     */
    @Test
    public void testUIDisplayOptimization() throws Exception {
        // 使用反射調用私有方法進行測試
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeMethod.setAccessible(true);

        // 測試案例1：混合中英文文本
        String input = "這是測試文本  with   multiple   spaces\r\n和換行符號。";
        String result = (String) normalizeMethod.invoke(googleOCR, input);

        // UI顯示模式應該保持視覺格式
        assertNotNull("結果不應為null", result);
        assertFalse("結果不應為空", result.isEmpty());
        assertTrue("應該保留基本格式", result.contains("這是測試文本"));
        assertTrue("應該保留英文內容", result.contains("with"));

        // 測試案例2：標點符號處理（UI模式保持原始格式）
        String punctuationInput = "測試...省略號，和，逗號。";
        String punctuationResult = (String) normalizeMethod.invoke(googleOCR, punctuationInput);

        assertNotNull("標點符號結果不應為null", punctuationResult);
        assertTrue("應該包含原始內容", punctuationResult.contains("測試"));
        assertTrue("UI模式應該保留省略號", punctuationResult.contains("..."));
    }

    /**
     * 測試UI顯示優化的一致性
     */
    @Test
    public void testUIDisplayConsistency() throws Exception {
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeMethod.setAccessible(true);

        // 測試案例1：省略號保留（UI顯示保持原樣）
        String ellipsisInput = "這是測試...文本";
        String ellipsisResult = (String) normalizeMethod.invoke(googleOCR, ellipsisInput);

        assertNotNull("省略號結果不應為null", ellipsisResult);
        assertTrue("UI模式應該保留省略號", ellipsisResult.contains("..."));
        assertTrue("應該包含原始內容", ellipsisResult.contains("這是測試"));

        // 測試案例2：特殊符號保留（UI顯示保持原樣）
        String symbolInput = "測試&符號@處理#效果";
        String symbolResult = (String) normalizeMethod.invoke(googleOCR, symbolInput);

        assertNotNull("符號結果不應為null", symbolResult);
        assertTrue("UI模式應該保留&符號", symbolResult.contains("&"));
        assertTrue("UI模式應該保留@符號", symbolResult.contains("@"));
        assertTrue("UI模式應該保留#符號", symbolResult.contains("#"));

        // 測試案例3：中英文間距保持原樣（UI顯示不添加額外空格）
        String spacingInput = "中文English混合123數字";
        String spacingResult = (String) normalizeMethod.invoke(googleOCR, spacingInput);

        assertNotNull("間距結果不應為null", spacingResult);
        // UI模式應該保持原始格式，不添加額外空格
        assertTrue("應該包含原始內容", spacingResult.contains("中文English"));
        assertTrue("應該包含數字", spacingResult.contains("123"));
    }

    /**
     * 測試單一職責原則：GoogleOCR專注於UI顯示優化
     */
    @Test
    public void testSingleResponsibilityPrinciple() throws Exception {
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeMethod.setAccessible(true);

        String testInput = "測試文本  with   spaces\r\n";

        // GoogleOCR應該只進行UI顯示優化
        String result = (String) normalizeMethod.invoke(googleOCR, testInput);

        assertNotNull("結果不應為null", result);
        assertFalse("結果不應為空", result.isEmpty());

        // 驗證基本的UI顯示優化功能
        assertTrue("應該包含原始內容", result.contains("測試文本"));
        assertTrue("應該包含英文內容", result.contains("with"));

        // 驗證空格標準化
        assertFalse("不應該包含多個連續空格", result.contains("   "));

        // 驗證換行符標準化
        assertFalse("不應該包含CRLF", result.contains("\r\n"));
    }

    /**
     * 測試性能優化 - 緩存的正則表達式模式
     */
    @Test
    public void testPerformanceOptimization() throws Exception {
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeMethod.setAccessible(true);
        
        String testInput = "測試\r\n換行\r和\r\n多種\r換行符號  以及   多個空格";
        
        // 測試多次調用的性能（緩存的正則表達式應該提升性能）
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 100; i++) {
            String result = (String) normalizeMethod.invoke(googleOCR, testInput);
            assertNotNull("第" + i + "次調用結果不應為null", result);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 100次調用應該在合理時間內完成（假設小於1秒）
        assertTrue("性能測試：100次調用應該在1秒內完成，實際耗時：" + duration + "ms", duration < 1000);
    }

    /**
     * 測試邊界情況
     */
    @Test
    public void testEdgeCases() throws Exception {
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeMethod.setAccessible(true);

        // 測試null輸入
        String nullResult = (String) normalizeMethod.invoke(googleOCR, (String) null);
        assertEquals("null輸入應該返回空字符串", "", nullResult);

        // 測試空字符串
        String emptyResult = (String) normalizeMethod.invoke(googleOCR, "");
        assertEquals("空字符串應該返回空字符串", "", emptyResult);

        // 測試只有空格的字符串
        String spacesResult = (String) normalizeMethod.invoke(googleOCR, "   ");
        assertNotNull("只有空格的結果不應為null", spacesResult);

        // 測試極長文本
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longText.append("測試文本").append(i).append(" ");
        }
        String longResult = (String) normalizeMethod.invoke(googleOCR, longText.toString());
        assertNotNull("極長文本結果不應為null", longResult);
        assertTrue("極長文本應該被正確處理", longResult.length() > 0);
    }

    /**
     * 測試架構分離：GoogleOCR不再包含TTS相關功能
     */
    @Test
    public void testArchitecturalSeparation() {
        // 驗證GoogleOCR不再包含TTS相關方法
        Method[] methods = GoogleOCR.class.getDeclaredMethods();

        for (Method method : methods) {
            String methodName = method.getName();
            // 確保沒有TTS相關的方法名
            assertFalse("不應該包含TTS相關方法: " + methodName,
                methodName.toLowerCase().contains("tts"));
            assertFalse("不應該包含performOCRForTTS方法: " + methodName,
                methodName.equals("performOCRForTTS"));
            assertFalse("不應該包含optimizeForTTS方法: " + methodName,
                methodName.equals("optimizeForTTS"));
        }

        // 驗證不再有OutputType枚舉
        Class<?>[] innerClasses = GoogleOCR.class.getDeclaredClasses();
        for (Class<?> innerClass : innerClasses) {
            assertFalse("不應該包含OutputType枚舉: " + innerClass.getSimpleName(),
                innerClass.getSimpleName().equals("OutputType"));
        }
    }
}
