package com.xy.demo.ocr;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Method;

/**
 * 增強版GoogleOCR測試 - 測試UI顯示優化功能
 * 專注於測試單一職責的UI文本處理效果
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class GoogleOCREnhancedTest {

    private GoogleOCR googleOCR;
    
    @Mock
    private OCRCallback mockCallback;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        googleOCR = new GoogleOCR();
    }

    /**
     * 測試UI顯示模式的文本優化
     */
    @Test
    public void testUIDisplayOptimization() throws Exception {
        // 使用反射調用私有方法進行測試
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        normalizeMethod.setAccessible(true);

        // 測試案例1：混合中英文文本
        String input = "這是測試文本  with   multiple   spaces\r\n和換行符號。";
        String result = (String) normalizeMethod.invoke(googleOCR, input);

        // UI顯示模式應該保持視覺格式
        assertNotNull("結果不應為null", result);
        assertFalse("結果不應為空", result.isEmpty());
        assertTrue("應該保留基本格式", result.contains("這是測試文本"));
        assertTrue("應該保留英文內容", result.contains("with"));

        // 測試案例2：標點符號處理（UI模式保持原始格式）
        String punctuationInput = "測試...省略號，和，逗號。";
        String punctuationResult = (String) normalizeMethod.invoke(googleOCR, punctuationInput);

        assertNotNull("標點符號結果不應為null", punctuationResult);
        assertTrue("應該包含原始內容", punctuationResult.contains("測試"));
        assertTrue("UI模式應該保留省略號", punctuationResult.contains("..."));
    }

    /**
     * 測試TTS模式的文本優化
     */
    @Test
    public void testTTSModeOptimization() throws Exception {
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.class);
        normalizeMethod.setAccessible(true);
        
        // 測試案例1：省略號轉換
        String ellipsisInput = "這是測試...文本";
        String ellipsisResult = (String) normalizeMethod.invoke(googleOCR, ellipsisInput, GoogleOCR.OutputType.TTS);
        
        assertNotNull("省略號結果不應為null", ellipsisResult);
        assertTrue("省略號應該被轉換為句號", ellipsisResult.contains("."));
        assertFalse("不應該包含省略號", ellipsisResult.contains("..."));
        
        // 測試案例2：特殊符號處理
        String symbolInput = "測試&符號@處理#效果";
        String symbolResult = (String) normalizeMethod.invoke(googleOCR, symbolInput, GoogleOCR.OutputType.TTS);
        
        assertNotNull("符號結果不應為null", symbolResult);
        assertTrue("&應該被轉換", symbolResult.contains("和"));
        assertTrue("@應該被轉換", symbolResult.contains("at"));
        assertTrue("#應該被轉換", symbolResult.contains("號"));
        
        // 測試案例3：中英文間距處理
        String spacingInput = "中文English混合123數字";
        String spacingResult = (String) normalizeMethod.invoke(googleOCR, spacingInput, GoogleOCR.OutputType.TTS);
        
        assertNotNull("間距結果不應為null", spacingResult);
        // TTS模式應該在中英文間添加空格以提升發音清晰度
        assertTrue("中英文間應該有空格", spacingResult.contains("中文 English"));
        assertTrue("英文數字間應該有空格", spacingResult.contains("English 混合 123"));
    }

    /**
     * 測試向後兼容性
     */
    @Test
    public void testBackwardCompatibility() throws Exception {
        Method oldNormalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class);
        oldNormalizeMethod.setAccessible(true);
        
        Method newNormalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.class);
        newNormalizeMethod.setAccessible(true);
        
        String testInput = "測試文本  with   spaces\r\n";
        
        // 舊方法調用
        String oldResult = (String) oldNormalizeMethod.invoke(googleOCR, testInput);
        
        // 新方法DISPLAY模式調用
        String newResult = (String) newNormalizeMethod.invoke(googleOCR, testInput, GoogleOCR.OutputType.DISPLAY);
        
        assertNotNull("舊方法結果不應為null", oldResult);
        assertNotNull("新方法結果不應為null", newResult);
        
        // 向後兼容性：舊方法應該與DISPLAY模式產生相似結果
        assertEquals("向後兼容性測試", oldResult.trim(), newResult.trim());
    }

    /**
     * 測試性能優化 - 緩存的正則表達式模式
     */
    @Test
    public void testPerformanceOptimization() throws Exception {
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.class);
        normalizeMethod.setAccessible(true);
        
        String testInput = "測試\r\n換行\r和\r\n多種\r換行符號  以及   多個空格";
        
        // 測試多次調用的性能（緩存的正則表達式應該提升性能）
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 100; i++) {
            String result = (String) normalizeMethod.invoke(googleOCR, testInput, GoogleOCR.OutputType.DISPLAY);
            assertNotNull("第" + i + "次調用結果不應為null", result);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 100次調用應該在合理時間內完成（假設小於1秒）
        assertTrue("性能測試：100次調用應該在1秒內完成，實際耗時：" + duration + "ms", duration < 1000);
    }

    /**
     * 測試邊界情況
     */
    @Test
    public void testEdgeCases() throws Exception {
        Method normalizeMethod = GoogleOCR.class.getDeclaredMethod("normalizeTextForDisplay", String.class, GoogleOCR.OutputType.class);
        normalizeMethod.setAccessible(true);
        
        // 測試null輸入
        String nullResult = (String) normalizeMethod.invoke(googleOCR, null, GoogleOCR.OutputType.DISPLAY);
        assertNull("null輸入應該返回null", nullResult);
        
        // 測試空字符串
        String emptyResult = (String) normalizeMethod.invoke(googleOCR, "", GoogleOCR.OutputType.TTS);
        assertEquals("空字符串應該返回空字符串", "", emptyResult);
        
        // 測試只有空格的字符串
        String spacesResult = (String) normalizeMethod.invoke(googleOCR, "   ", GoogleOCR.OutputType.DISPLAY);
        assertNotNull("只有空格的結果不應為null", spacesResult);
        
        // 測試極長文本
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longText.append("測試文本").append(i).append(" ");
        }
        String longResult = (String) normalizeMethod.invoke(googleOCR, longText.toString(), GoogleOCR.OutputType.TTS);
        assertNotNull("極長文本結果不應為null", longResult);
        assertTrue("極長文本應該被正確處理", longResult.length() > 0);
    }

    /**
     * 測試TTS專用API
     */
    @Test
    public void testTTSSpecificAPI() {
        // 測試TTS專用的performOCRForTTS方法是否存在
        try {
            Method ttsMethod = GoogleOCR.class.getDeclaredMethod("performOCRForTTS", 
                String.class, byte[].class, int.class, int.class, OCRCallback.class);
            assertNotNull("TTS專用方法應該存在", ttsMethod);
        } catch (NoSuchMethodException e) {
            fail("TTS專用方法不存在：" + e.getMessage());
        }
    }

    /**
     * 測試OutputType枚舉
     */
    @Test
    public void testOutputTypeEnum() {
        // 測試枚舉值
        GoogleOCR.OutputType[] types = GoogleOCR.OutputType.values();
        assertEquals("應該有兩個輸出類型", 2, types.length);
        
        boolean hasDisplay = false;
        boolean hasTTS = false;
        
        for (GoogleOCR.OutputType type : types) {
            if (type.name().equals("DISPLAY")) hasDisplay = true;
            if (type.name().equals("TTS")) hasTTS = true;
        }
        
        assertTrue("應該包含DISPLAY類型", hasDisplay);
        assertTrue("應該包含TTS類型", hasTTS);
    }
}
