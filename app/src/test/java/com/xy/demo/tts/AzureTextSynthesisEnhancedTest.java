package com.xy.demo.tts;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Method;
import java.util.List;
import java.util.ArrayList;

/**
 * 增強版AzureTextSynthesis測試 - 測試55%閾值、增強停頓處理和韻律改進
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class AzureTextSynthesisEnhancedTest {

    private AzureTextSynthesis azureTTS;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        azureTTS = new AzureTextSynthesis();
    }

    /**
     * 測試55%語言閾值判斷
     */
    @Test
    public void testLanguageThreshold55Percent() throws Exception {
        // 使用反射創建LanguageRatio對象進行測試
        Class<?> languageRatioClass = Class.forName("com.xy.demo.tts.AzureTextSynthesis$LanguageRatio");
        
        // 測試英文主導（56%英文，30%中文）
        Object englishDominated = languageRatioClass.getDeclaredConstructor(double.class, double.class, int.class)
            .newInstance(0.30, 0.56, 100);
        Method isEnglishDominated = languageRatioClass.getDeclaredMethod("isEnglishDominated");
        boolean englishResult = (Boolean) isEnglishDominated.invoke(englishDominated);
        assertTrue("56%英文應該被判定為英文主導", englishResult);
        
        // 測試中文主導（60%中文，25%英文）
        Object chineseDominated = languageRatioClass.getDeclaredConstructor(double.class, double.class, int.class)
            .newInstance(0.60, 0.25, 100);
        Method isChineseDominated = languageRatioClass.getDeclaredMethod("isChineseDominated");
        boolean chineseResult = (Boolean) isChineseDominated.invoke(chineseDominated);
        assertTrue("60%中文應該被判定為中文主導", chineseResult);
        
        // 測試邊界情況（54%英文，40%中文）
        Object borderlineEnglish = languageRatioClass.getDeclaredConstructor(double.class, double.class, int.class)
            .newInstance(0.40, 0.54, 100);
        boolean borderlineEnglishResult = (Boolean) isEnglishDominated.invoke(borderlineEnglish);
        assertFalse("54%英文不應該被判定為英文主導", borderlineEnglishResult);
        
        // 測試平衡情況（45%英文，45%中文）
        Object balanced = languageRatioClass.getDeclaredConstructor(double.class, double.class, int.class)
            .newInstance(0.45, 0.45, 100);
        boolean balancedEnglishResult = (Boolean) isEnglishDominated.invoke(balanced);
        boolean balancedChineseResult = (Boolean) isChineseDominated.invoke(balanced);
        assertFalse("平衡情況不應該被判定為英文主導", balancedEnglishResult);
        assertFalse("平衡情況不應該被判定為中文主導", balancedChineseResult);
    }

    /**
     * 測試智能數字語言選擇（55%閾值）
     */
    @Test
    public void testIntelligentNumberLanguageSelection() throws Exception {
        Method getNumberLanguageMethod = AzureTextSynthesis.class.getDeclaredMethod(
            "getIntelligentNumberLanguageCode", 
            Class.forName("com.xy.demo.tts.AzureTextSynthesis$LanguageRatio"), 
            boolean.class);
        getNumberLanguageMethod.setAccessible(true);
        
        Class<?> languageRatioClass = Class.forName("com.xy.demo.tts.AzureTextSynthesis$LanguageRatio");
        
        // 測試英文主導情況（60%英文）
        Object englishDominated = languageRatioClass.getDeclaredConstructor(double.class, double.class, int.class)
            .newInstance(0.30, 0.60, 100);
        String englishResult = (String) getNumberLanguageMethod.invoke(azureTTS, englishDominated, false);
        assertEquals("英文主導時數字應該用英文", "en-US", englishResult);
        
        // 測試中文主導情況（65%中文）
        Object chineseDominated = languageRatioClass.getDeclaredConstructor(double.class, double.class, int.class)
            .newInstance(0.65, 0.25, 100);
        String chineseResult = (String) getNumberLanguageMethod.invoke(azureTTS, chineseDominated, false);
        assertTrue("中文主導時數字應該用中文", 
            chineseResult.equals("zh-CN") || chineseResult.equals("zh-HK"));
        
        // 測試平衡情況，英文略高（50%英文，45%中文）
        Object englishSlightAdvantage = languageRatioClass.getDeclaredConstructor(double.class, double.class, int.class)
            .newInstance(0.45, 0.50, 100);
        String balancedResult = (String) getNumberLanguageMethod.invoke(azureTTS, englishSlightAdvantage, false);
        assertEquals("平衡情況下英文略高時應該用英文", "en-US", balancedResult);
    }

    /**
     * 測試增強的停頓處理
     */
    @Test
    public void testEnhancedPauseHandling() throws Exception {
        Method addPausesMethod = AzureTextSynthesis.class.getDeclaredMethod(
            "addIntelligentPausesWithSpaceInfo", String.class, String.class, String.class);
        addPausesMethod.setAccessible(true);
        
        // 測試中英文切換停頓減少50ms（從350ms減至300ms）
        String mixedText = "這是中文English文本";
        String result = (String) addPausesMethod.invoke(azureTTS, mixedText, "CHINESE", mixedText);
        
        assertNotNull("結果不應為null", result);
        assertTrue("應該包含300ms停頓", result.contains("300ms"));
        assertFalse("不應該包含350ms停頓", result.contains("350ms"));
        
        // 測試中文詞間空格增強停頓
        String chineseWithSpaces = "這是 測試 文本";
        String spaceResult = (String) addPausesMethod.invoke(azureTTS, chineseWithSpaces, "CHINESE", chineseWithSpaces);
        
        assertNotNull("空格結果不應為null", spaceResult);
        assertTrue("應該包含詞間停頓", spaceResult.contains("break time="));
    }

    /**
     * 測試韻律增強功能
     */
    @Test
    public void testProsodyEnhancements() throws Exception {
        Method addProsodyMethod = AzureTextSynthesis.class.getDeclaredMethod(
            "addProsodyEnhancements", String.class, String.class);
        addProsodyMethod.setAccessible(true);
        
        // 測試疑問句語調
        String questionText = "這是問題嗎？";
        String questionResult = (String) addProsodyMethod.invoke(azureTTS, questionText, "CHINESE");
        
        assertNotNull("疑問句結果不應為null", questionResult);
        assertTrue("疑問句應該包含prosody標籤", questionResult.contains("<prosody"));
        assertTrue("疑問句應該有音調提升", questionResult.contains("pitch=\"+"));
        
        // 測試感嘆句強調
        String exclamationText = "太棒了！";
        String exclamationResult = (String) addProsodyMethod.invoke(azureTTS, exclamationText, "CHINESE");
        
        assertNotNull("感嘆句結果不應為null", exclamationResult);
        assertTrue("感嘆句應該包含prosody標籤", exclamationResult.contains("<prosody"));
        assertTrue("感嘆句應該有音量提升", exclamationResult.contains("volume=\"+"));
        
        // 測試數字清晰發音
        String numberText = "數字123測試";
        String numberResult = (String) addProsodyMethod.invoke(azureTTS, numberText, "CHINESE");
        
        assertNotNull("數字結果不應為null", numberResult);
        assertTrue("數字應該有慢速發音", numberResult.contains("rate=\"0.8\""));
        
        // 測試重要詞彙強調
        String importantText = "這很重要";
        String importantResult = (String) addProsodyMethod.invoke(azureTTS, importantText, "CHINESE");
        
        assertNotNull("重要詞彙結果不應為null", importantResult);
        assertTrue("重要詞彙應該有強調", importantResult.contains("<emphasis"));
    }

    /**
     * 測試句子級別分析（\n邊界）
     */
    @Test
    public void testSentenceLevelAnalysis() throws Exception {
        Method processBySentencesMethod = AzureTextSynthesis.class.getDeclaredMethod(
            "processTextBySentences", String.class, float.class);
        processBySentencesMethod.setAccessible(true);
        
        // 測試多句子文本（用\n分隔）
        String multiSentenceText = "這是第一句中文。\nThis is English sentence.\n這是第三句中文。";
        String result = (String) processBySentencesMethod.invoke(azureTTS, multiSentenceText, 1.0f);
        
        assertNotNull("多句子結果不應為null", result);
        assertTrue("應該包含SSML結構", result.contains("<speak"));
        assertTrue("應該包含voice標籤", result.contains("<voice"));
        assertTrue("應該包含句子間停頓", result.contains("600ms"));
        
        // 測試空行處理
        String textWithEmptyLines = "第一句\n\n第二句";
        String emptyLineResult = (String) processBySentencesMethod.invoke(azureTTS, textWithEmptyLines, 1.0f);
        
        assertNotNull("空行結果不應為null", emptyLineResult);
        assertTrue("空行應該有段落分隔", emptyLineResult.contains("800ms"));
    }

    /**
     * 測試中文字符檢測
     */
    @Test
    public void testChineseCharacterDetection() throws Exception {
        Method isChineseMethod = AzureTextSynthesis.class.getDeclaredMethod("isChineseCharacter", char.class);
        isChineseMethod.setAccessible(true);
        
        // 測試基本漢字
        assertTrue("基本漢字應該被識別", (Boolean) isChineseMethod.invoke(azureTTS, '中'));
        assertTrue("基本漢字應該被識別", (Boolean) isChineseMethod.invoke(azureTTS, '文'));
        
        // 測試非中文字符
        assertFalse("英文字符不應該被識別為中文", (Boolean) isChineseMethod.invoke(azureTTS, 'A'));
        assertFalse("數字不應該被識別為中文", (Boolean) isChineseMethod.invoke(azureTTS, '1'));
        assertFalse("標點符號不應該被識別為中文", (Boolean) isChineseMethod.invoke(azureTTS, '.'));
    }

    /**
     * 測試性能改進
     */
    @Test
    public void testPerformanceImprovements() throws Exception {
        Method processBySentencesMethod = AzureTextSynthesis.class.getDeclaredMethod(
            "processTextBySentences", String.class, float.class);
        processBySentencesMethod.setAccessible(true);
        
        // 測試較長文本的處理性能
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 10; i++) {
            longText.append("這是第").append(i + 1).append("句中文測試。\n");
            longText.append("This is sentence ").append(i + 1).append(" in English.\n");
        }
        
        long startTime = System.currentTimeMillis();
        String result = (String) processBySentencesMethod.invoke(azureTTS, longText.toString(), 1.0f);
        long endTime = System.currentTimeMillis();
        
        assertNotNull("長文本結果不應為null", result);
        assertTrue("長文本處理應該在合理時間內完成", (endTime - startTime) < 5000); // 5秒內
        assertTrue("結果應該包含所有句子的處理", result.length() > longText.length());
    }

    /**
     * 測試邊界情況和錯誤處理
     */
    @Test
    public void testEdgeCasesAndErrorHandling() throws Exception {
        Method processBySentencesMethod = AzureTextSynthesis.class.getDeclaredMethod(
            "processTextBySentences", String.class, float.class);
        processBySentencesMethod.setAccessible(true);
        
        // 測試null輸入
        String nullResult = (String) processBySentencesMethod.invoke(azureTTS, null, 1.0f);
        assertNotNull("null輸入應該有回退處理", nullResult);
        
        // 測試空字符串
        String emptyResult = (String) processBySentencesMethod.invoke(azureTTS, "", 1.0f);
        assertNotNull("空字符串應該有回退處理", emptyResult);
        
        // 測試只有換行符的字符串
        String newlineOnlyResult = (String) processBySentencesMethod.invoke(azureTTS, "\n\n\n", 1.0f);
        assertNotNull("只有換行符應該有處理", newlineOnlyResult);
        
        // 測試特殊字符
        String specialCharsResult = (String) processBySentencesMethod.invoke(azureTTS, "測試<>&\"'特殊字符", 1.0f);
        assertNotNull("特殊字符應該被正確處理", specialCharsResult);
        assertTrue("特殊字符應該被轉義", specialCharsResult.contains("&lt;") || !specialCharsResult.contains("<測試"));
    }
}
