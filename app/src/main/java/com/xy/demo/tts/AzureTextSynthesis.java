package com.xy.demo.tts;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.microsoft.cognitiveservices.speech.Connection;
import com.microsoft.cognitiveservices.speech.PropertyId;
import com.microsoft.cognitiveservices.speech.SpeechConfig;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisCancellationDetails;
import com.microsoft.cognitiveservices.speech.SpeechSynthesisOutputFormat;
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer;
import com.xy.demo.utils.PreferenceManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AzureTextSynthesis {
    private static String speechSubscriptionKey = "767ddc9821bc4c868eab093c2c8b080c";
    private static String serviceRegion = "southeastasia";
    private SpeechConfig speechConfig;
    private SpeechSynthesizer synthesizer;
    public Connection connection;
    private AudioTrack audioTrack;
    private final Object synchronizedLock=new Object();
    public AtomicBoolean stopstate =null;
    public AtomicBoolean isPaused=null;
    public String[] voiceSet ={"zh-HK-HiuMaanNeural","zh-TW-HsiaoChenNeural","en-GB-SoniaNeural"};
    private ExecutorService ttsThread;
    private boolean useSSML = true; // SSML開關，用於調試
    
    // 多語言文本處理相關
    private PreferenceManager preferenceManager;
    
    // Unicode 屬性正則表達式模式
    private static final Pattern CHINESE_PATTERN = Pattern.compile("\\p{IsHan}+");
    private static final Pattern LATIN_PATTERN = Pattern.compile("\\p{IsLatin}+");
    private static final Pattern PUNCTUATION_PATTERN = Pattern.compile("\\p{P}+");
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\p{N}+");
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\p{Z}+");
    
    // 【Phase 3 新增】Web Link檢測模式
    private static final Pattern WEBLINK_PATTERN = Pattern.compile(
        "(?i)\\b(?:https?://|www\\.|ftp://)[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+", 
        Pattern.CASE_INSENSITIVE
    );
    
    // 🔥 修復：BREAK_MARKER_PATTERN已移除，因為現在直接插入<break>元素


    public AzureTextSynthesis(String voice) {
        this(voice, null);
    }
    
    public AzureTextSynthesis(String voice, Context context) {
        // 優化音頻配置以獲得更好的性能
        initializeOptimizedAudioTrack();
        
        stopstate = new AtomicBoolean(true);
        isPaused = new AtomicBoolean(false);
        
        // 初始化PreferenceManager（如果提供了Context）
        if (context != null) {
            this.preferenceManager = new PreferenceManager(context);
            Log.i("AzureTTS", "Multi-language TTS initialized with user preferences");
        } else {
            Log.w("AzureTTS", "Context not provided, multi-language features will use defaults");
        }
        
        this.createSynthesizer(voice);
    }
    
    /**
     * 初始化優化的AudioTrack配置
     * 使用保守的緩衝區大小和音頻參數以提高穩定性
     */
    private void initializeOptimizedAudioTrack() {
        // 音頻配置常量
        final int SAMPLE_RATE = 24000;
        final int CHANNEL_CONFIG = AudioFormat.CHANNEL_OUT_MONO;
        final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
        
        // 計算最佳緩衝區大小
        int minBufferSize = AudioTrack.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);
        
        // 修復MediaCodec錯誤: 使用更大的緩衝區以避免渲染錯誤
        // 從3倍增加到5倍最小緩衝區大小，提高穩定性
        int optimizedBufferSize = Math.max(minBufferSize * 5, 16384);
        
        Log.i("AzureTTS", String.format("Audio buffer configuration - Min: %d, Optimized: %d", 
            minBufferSize, optimizedBufferSize));
        
        try {
            // 嘗試更保守的AudioTrack配置
            this.audioTrack = createStableAudioTrack(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT, optimizedBufferSize);
            
            // 驗證AudioTrack初始化成功
            if (audioTrack.getState() != AudioTrack.STATE_INITIALIZED) {
                Log.w("AzureTTS", "Primary AudioTrack initialization failed, trying fallback");
                throw new RuntimeException("Failed to initialize AudioTrack");
            }
            
            // 設置播放位置更新回調以監控性能
            setupAudioTrackMonitoring();
            
            Log.i("AzureTTS", "Stable AudioTrack initialized successfully");
            
        } catch (Exception e) {
            Log.e("AzureTTS", "Error initializing stable AudioTrack", e);
            // 回退到基本配置
            initializeFallbackAudioTrack();
        }
    }
    
    /**
     * 創建穩定的AudioTrack配置，避免MediaCodec錯誤
     */
    private AudioTrack createStableAudioTrack(int sampleRate, int channelConfig, int audioFormat, int bufferSize) {
        // 首先嘗試不使用低延遲標誌的配置
        try {
            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    // 移除 FLAG_LOW_LATENCY 以避免MediaCodec渲染錯誤
                    .build();
            
            AudioFormat format = new AudioFormat.Builder()
                    .setEncoding(audioFormat)
                    .setSampleRate(sampleRate)
                    .setChannelMask(channelConfig)
                    .build();
            
            AudioTrack track = new AudioTrack(
                audioAttributes,
                format,
                bufferSize,
                AudioTrack.MODE_STREAM,
                AudioManager.AUDIO_SESSION_ID_GENERATE
            );
            
            Log.i("AzureTTS", "Created stable AudioTrack without low-latency flag");
            return track;
            
        } catch (Exception e) {
            Log.w("AzureTTS", "Stable AudioTrack creation failed, trying legacy mode", e);
            // 回退到最保守的配置
            return createLegacyAudioTrack(sampleRate, channelConfig, audioFormat, bufferSize);
        }
    }
    
    /**
     * 創建舊版AudioTrack配置（最保守）
     */
    private AudioTrack createLegacyAudioTrack(int sampleRate, int channelConfig, int audioFormat, int bufferSize) {
        return new AudioTrack(
            AudioManager.STREAM_MUSIC,  // 使用舊版流類型
            sampleRate,
            channelConfig,
            audioFormat,
            bufferSize,
            AudioTrack.MODE_STREAM
        );
    }
    
    /**
     * 回退音頻配置（如果優化配置失敗）
     */
    private void initializeFallbackAudioTrack() {
        Log.w("AzureTTS", "Using fallback AudioTrack configuration");
        
        this.audioTrack = new AudioTrack(
                new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                        .build(),
                new AudioFormat.Builder()
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .setSampleRate(24000)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .build(),
                AudioTrack.getMinBufferSize(24000, AudioFormat.CHANNEL_OUT_MONO, 
                    AudioFormat.ENCODING_PCM_16BIT) * 2,
                AudioTrack.MODE_STREAM,
                AudioManager.AUDIO_SESSION_ID_GENERATE);
    }
    
    /**
     * 設置AudioTrack監控以優化性能和檢測渲染錯誤
     */
    private void setupAudioTrackMonitoring() {
        if (audioTrack == null) return;
        
        try {
            // 設置播放位置更新監聽器（用於性能監控）
            audioTrack.setPositionNotificationPeriod(2000); // 每2000幀更新一次，減少監控開銷
            
            audioTrack.setPlaybackPositionUpdateListener(new AudioTrack.OnPlaybackPositionUpdateListener() {
                private int consecutiveErrors = 0;
                private long lastPlaybackPosition = 0;
                
                @Override
                public void onMarkerReached(AudioTrack track) {
                    Log.d("AzureTTS", "Audio marker reached");
                }
                
                @Override
                public void onPeriodicNotification(AudioTrack track) {
                    try {
                        // 檢測AudioTrack狀態和錯誤
                        int playState = track.getPlayState();
                        int playbackHeadPosition = track.getPlaybackHeadPosition();
                        
                        // 檢測播放停滯（可能是MediaCodec錯誤的症狀）
                        if (playState == AudioTrack.PLAYSTATE_PLAYING) {
                            if (playbackHeadPosition == lastPlaybackPosition) {
                                consecutiveErrors++;
                                Log.w("AzureTTS", String.format("Audio playback stalled. Position: %d, Consecutive errors: %d", 
                                    playbackHeadPosition, consecutiveErrors));
                                
                                // 如果連續多次檢測到停滯，可能需要重置AudioTrack
                                if (consecutiveErrors >= 3) {
                                    Log.e("AzureTTS", "Multiple consecutive audio stalls detected, AudioTrack may need reset");
                                    consecutiveErrors = 0; // 重置計數器
                                }
                            } else {
                                consecutiveErrors = 0; // 重置錯誤計數器
                                
                                // 計算緩衝區狀態
                                int bufferSizeInFrames = track.getBufferSizeInFrames();
                                if (bufferSizeInFrames > 0) {
                                    float bufferUsage = (float) playbackHeadPosition / bufferSizeInFrames;
                                    
                                    // 調整警告閾值，減少日誌噪音
                                    if (bufferUsage > 0.95f) {
                                        Log.w("AzureTTS", String.format("Critical buffer usage: %.1f%%", bufferUsage * 100));
                                    }
                                }
                            }
                            
                            lastPlaybackPosition = playbackHeadPosition;
                        }
                        
                    } catch (Exception e) {
                        Log.e("AzureTTS", "Error in audio monitoring", e);
                    }
                }
            });
            
            Log.d("AzureTTS", "AudioTrack monitoring setup completed");
            
        } catch (Exception e) {
            Log.e("AzureTTS", "Failed to setup AudioTrack monitoring", e);
        }
    }
    public void createSynthesizer(String voice){
        try {
//            String destPath=Environment.getExternalStorageDirectory().getAbsolutePath() + "/tts_output.wav";
//            Log.i("check",destPath);
//            AudioConfig fileOutput = AudioConfig.fromWavFileOutput(destPath);
            if(ttsThread!=null && !ttsThread.isShutdown()){
                ttsThread.shutdown();
                try {
                    // 等待線程池正常關閉
                    if (!ttsThread.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                        ttsThread.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    ttsThread.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            ttsThread = Executors.newSingleThreadExecutor(r -> {
                Thread thread = new Thread(r, "AzureTTS-Thread");
                thread.setDaemon(true); // 設為守護線程，避免阻止應用退出
                return thread;
            });
            assert(ttsThread != null);

            if (synthesizer != null) {
                speechConfig.close();
                synthesizer.close();
                connection.close();
            }
            // Initialize speech synthesizer and its dependencies
            speechConfig = SpeechConfig.fromSubscription(speechSubscriptionKey, serviceRegion);
            assert(speechConfig != null);
            speechConfig.setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat.Raw24Khz16BitMonoPcm);
            speechConfig.setSpeechSynthesisVoiceName(mapVoice(voice));
            synthesizer = new SpeechSynthesizer(speechConfig, null);
            connection = Connection.fromSpeechSynthesizer(synthesizer);

            connection.connected.addEventListener((o, e) -> {
                Log.i("check","Connection established.\n");
            });

            connection.disconnected.addEventListener((o, e) -> {
                Log.i("check","Disconnected.\n");
                connection.openConnection(true);
            });

            synthesizer.SynthesisStarted.addEventListener((o, e) -> {
                Log.i("check",
                        "Synthesis started. Result Id: "+e.getResult().getResultId().toString());
                e.close();
            });

            synthesizer.Synthesizing.addEventListener((o, e) -> {
//                Log.i("check",
//                        "Synthesizing. received %d bytes "+ e.getResult().getAudioLength());
                e.close();
            });

            synthesizer.SynthesisCompleted.addEventListener((o, e) -> {
                Log.i("check","Synthesis finished.\n");
                Log.i("check","\tFirst byte latency: " + e.getResult().getProperties().getProperty(PropertyId.SpeechServiceResponse_SynthesisFirstByteLatencyMs) + " ms.");
                Log.i("check","\tFinish latency: " + e.getResult().getProperties().getProperty(PropertyId.SpeechServiceResponse_SynthesisFinishLatencyMs) + " ms.");
                e.close();
            });

            synthesizer.SynthesisCanceled.addEventListener((o, e) -> {
                String cancellationDetails =
                        SpeechSynthesisCancellationDetails.fromResult(e.getResult()).toString();
                Log.i("check","Error synthesizing. Result ID: " + e.getResult().getResultId() +
                        ". Error detail: " + System.lineSeparator() + cancellationDetails);
                e.close();
            });

            connection.openConnection(true);
//            AudioConfig audioConfig=AudioConfig.fromWavFileOutput()
//            assert(synthesizer != null);
        } catch (Exception ex) {
            Log.e("check", "unexpected in onCreate() " + ex.getMessage());
            assert(false);
        }
    }
    public void startPlaying(String inputText, float speed) {
        if (inputText == null || inputText.isEmpty()) {
            ttsThread.submit(new SynthesisRunnable(this.synthesizer, this.audioTrack, 
                this.synchronizedLock, this.stopstate, this.isPaused, "Empty!", speed));
        } else {
            String processedText;
            if (useSSML) {
                // 📝 簡化：統一使用MultilingualSSML，無需複雜度分析
                Log.d("AzureTTS", "Using multilingual SSML mode with JennyMultilingualNeural");
                processedText = wrapWithMultilingualSSML(inputText, speed);
            } else {
                // 使用純文本模式
                processedText = inputText;
                Log.d("AzureTTS", "Using plain text mode for TTS");
            }
            
            ttsThread.submit(new SynthesisRunnable(this.synthesizer, this.audioTrack, 
                this.synchronizedLock, this.stopstate, this.isPaused, processedText, speed));
        }
    }
    
    /**
     * 設置是否使用SSML（用於調試SSML問題）
     */
    public void setUseSSML(boolean useSSML) {
        this.useSSML = useSSML;
        Log.i("AzureTTS", "SSML mode set to: " + useSSML);
    }
    
    
    /**
     * 使用Unicode屬性將混合語言文本分割為TextSegment列表
     * @param text 原始混合語言文本
     * @return 分割後的文本片段列表
     */
    private List<TextSegment> segmentMixedLanguageText(String text) {
        if (text == null || text.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<TextSegment> segments = new ArrayList<>();
        int length = text.length();
        int currentPos = 0;
        
        while (currentPos < length) {
            TextSegment segment = findNextTextSegment(text, currentPos);
            if (segment != null && !segment.isEmpty()) {
                segments.add(segment);
                currentPos = segment.getEndIndex();
            } else {
                currentPos++; // 避免無限循環
            }
        }
        
        // 合併相鄰的相同語言片段
        return mergeAdjacentSegments(segments);
    }
    
    /**
     * 從指定位置開始尋找下一個文本片段
     * @param text 完整文本
     * @param startPos 開始位置
     * @return 找到的文本片段，如果沒有找到則返回null
     */
    private TextSegment findNextTextSegment(String text, int startPos) {
        if (startPos >= text.length()) {
            return null;
        }
        
        String remainingText = text.substring(startPos);
        
        // 按優先級檢查不同類型的字符
        TextSegment segment = null;
        
        // 🔥 修復：BREAK_MARKER檢測已移除，因為現在直接插入<break>元素
        
        // 【Phase 3 新增】1. Web Link檢測 (次高優先級，避免被其他模式誤分割)
        segment = findSegmentByPattern(remainingText, WEBLINK_PATTERN, TextSegment.Language.WEBLINK, startPos);
        if (segment != null) return segment;
        
        // 2. 中文字符
        segment = findSegmentByPattern(remainingText, CHINESE_PATTERN, TextSegment.Language.CHINESE, startPos);
        if (segment != null) return segment;
        
        // 3. 拉丁字符（英文）
        segment = findSegmentByPattern(remainingText, LATIN_PATTERN, TextSegment.Language.ENGLISH, startPos);
        if (segment != null) return segment;
        
        // 4. 數字
        segment = findSegmentByPattern(remainingText, NUMBER_PATTERN, TextSegment.Language.NUMBER, startPos);
        if (segment != null) return segment;
        
        // 5. 標點符號
        segment = findSegmentByPattern(remainingText, PUNCTUATION_PATTERN, TextSegment.Language.PUNCTUATION, startPos);
        if (segment != null) return segment;
        
        // 6. 空白字符
        segment = findSegmentByPattern(remainingText, WHITESPACE_PATTERN, TextSegment.Language.WHITESPACE, startPos);
        if (segment != null) return segment;
        
        // 6. 如果都沒匹配到，創建單字符片段
        char currentChar = text.charAt(startPos);
        return new TextSegment(String.valueOf(currentChar), TextSegment.Language.MIXED, startPos, startPos + 1);
    }
    
    /**
     * 使用正則表達式模式查找文本片段
     */
    @Nullable
    private TextSegment findSegmentByPattern(String text, Pattern pattern, TextSegment.Language language, int baseOffset) {
        Matcher matcher = pattern.matcher(text);
        if (matcher.find() && matcher.start() == 0) {
            String matchedText = matcher.group();
            int start = baseOffset + matcher.start();
            int end = baseOffset + matcher.end();
            return new TextSegment(matchedText, language, start, end);
        }
        return null;
    }
    
    /**
     * 合併相鄰的相同語言片段
     */
    private List<TextSegment> mergeAdjacentSegments(List<TextSegment> segments) {
        if (segments.size() <= 1) {
            return segments;
        }
        
        List<TextSegment> merged = new ArrayList<>();
        TextSegment current = segments.get(0);
        
        for (int i = 1; i < segments.size(); i++) {
            TextSegment next = segments.get(i);
            
            // 如果是相同語言且都是文本內容，則合併
            if (shouldMergeSegments(current, next)) {
                try {
                    current = current.merge(next);
                } catch (IllegalArgumentException e) {
                    // 合併失敗，保留當前片段並開始新的
                    merged.add(current);
                    current = next;
                }
            } else {
                merged.add(current);
                current = next;
            }
        }
        
        merged.add(current);
        return merged;
    }
    
    /**
     * 判斷兩個片段是否應該合併
     */
    private boolean shouldMergeSegments(TextSegment segment1, TextSegment segment2) {
        // 相同語言才能合併
        if (segment1.getLanguage() != segment2.getLanguage()) {
            return false;
        }
        
        // 只合併文本內容，不合併標點符號和空白字符
        return segment1.getLanguage().isTextContent() && segment2.getLanguage().isTextContent();
    }
    
    /**
     * 根據用戶的語音偏好獲取中文的語言代碼
     * 📝 修復：只基於VOICE_KEY設定，不考慮UI顯示語言
     * @return 適合的中文語言代碼
     */
    private String getUserPreferredChineseLanguageCode() {
        if (preferenceManager == null) {
            Log.d("AzureTTS", "PreferenceManager not available, using default Mandarin (zh-CN)");
            return "zh-CN"; // 默認普通話
        }
        
        String userVoice = preferenceManager.getVoice();
        Log.d("AzureTTS", String.format("User voice preference: %s", userVoice));
        
        // 📝 修復：完全基於語音偏好設定，不考慮UI顯示語言
        switch (userVoice) {
            case "English":
                // 用戶語音偏好英文時，中文文本使用粵語
                Log.d("AzureTTS", "User prefers English voice, Chinese text will use Cantonese (zh-HK)");
                return "zh-HK";
                
            case "Cantonese":
                Log.d("AzureTTS", "User prefers Cantonese voice, Chinese text will use Cantonese (zh-HK)");
                return "zh-HK";
                
            case "Mandarin":
                Log.d("AzureTTS", "User prefers Mandarin voice, Chinese text will use Mandarin (zh-CN)");
                return "zh-CN";
                
            default:
                Log.d("AzureTTS", String.format("Unknown voice preference '%s', defaulting to Mandarin (zh-CN)", userVoice));
                return "zh-CN";
        }
    }
    
    /**
     * 根據TextSegment語言類型獲取Azure支持的語言代碼
     * 用於JennyMultilingualNeural的<lang xml:lang>標籤
     * @param language TextSegment語言類型
     * @return Azure支持的語言代碼
     */
    private String getLanguageCodeForSegment(TextSegment.Language language) {
        switch (language) {
            case ENGLISH:
                // 純英文片段使用美式英語
                return "en-US";
                
            case CHINESE:
                // 中文片段根據用戶Language+Voice偏好選擇語言代碼
                return getUserPreferredChineseLanguageCode();
                
            case WEBLINK:
                // 【Phase 3 新增】Web Link總是使用英文朗讀
                return "en-US";
                
            case NUMBER:
            case PUNCTUATION:
            case MIXED:
            default:
                // 數字、標點符號和混合內容使用默認語言
                // 📝 修復：根據語音偏好決定，不基於UI顯示語言
                String userVoice = preferenceManager != null ? 
                    preferenceManager.getVoice() : "Cantonese";
                
                if ("English".equals(userVoice)) {
                    return "en-US";
                } else {
                    return getUserPreferredChineseLanguageCode();
                }
        }
    }
    
    /**
     * 根據TextSegment語言類型和語言占比獲取智能的語言代碼
     * 主要用於數字片段的智能語言選擇
     * @param language TextSegment語言類型
     * @param languageRatio 整句話的語言占比分析結果
     * @param isInWebLink 是否在Web Link中（Web Link中的數字總是用英文）
     * @return Azure支持的語言代碼
     */
    private String getLanguageCodeForSegment(TextSegment.Language language, 
                                           LanguageRatio languageRatio, 
                                           boolean isInWebLink) {
        switch (language) {
            case ENGLISH:
                // 純英文片段使用美式英語
                return "en-US";
                
            case CHINESE:
                // 中文片段根據用戶Language+Voice偏好選擇語言代碼
                return getUserPreferredChineseLanguageCode();
                
            case WEBLINK:
                // 【Phase 3 新增】Web Link總是使用英文朗讀
                return "en-US";
                
            case NUMBER:
                // 智能數字語言選擇
                return getIntelligentNumberLanguageCode(languageRatio, isInWebLink);
                
            case PUNCTUATION:
            case MIXED:
            default:
                // 標點符號和混合內容根據語音偏好決定
                String userVoice = preferenceManager != null ? 
                    preferenceManager.getVoice() : "Mandarin";
                
                if ("English".equals(userVoice)) {
                    return "en-US";
                } else {
                    return getUserPreferredChineseLanguageCode();
                }
        }
    }
    
    /**
     * 智能選擇數字的朗讀語言 - 使用55%閾值進行判斷
     * @param languageRatio 語言占比分析結果
     * @param isInWebLink 是否在Web Link中
     * @return 數字朗讀的語言代碼
     */
    private String getIntelligentNumberLanguageCode(LanguageRatio languageRatio, boolean isInWebLink) {
        // Web Link中的數字總是使用英文朗讀
        if (isInWebLink) {
            Log.d("AzureTTS", "Number in Web Link detected, using English");
            return "en-US";
        }
        
        // 如果沒有語言占比信息，使用默認邏輯
        if (languageRatio == null || languageRatio.totalChars == 0) {
            Log.d("AzureTTS", "No language ratio info, using default logic for numbers");
            return getLanguageCodeForSegment(TextSegment.Language.NUMBER);
        }
        
        // 根據語言占比智能選擇（使用55%閾值）
        if (languageRatio.isEnglishDominated()) {
            // 英文占比>55%且大於中文占比，數字用英文
            Log.d("AzureTTS", String.format("English-dominated sentence (%.1f%% vs %.1f%% Chinese), numbers in English",
                languageRatio.englishRatio * 100, languageRatio.chineseRatio * 100));
            return "en-US";

        } else if (languageRatio.isChineseDominated()) {
            // 中文占比>55%且大於英文占比，數字用中文（根據用戶偏好選擇粵語/普通話）
            String chineseCode = getUserPreferredChineseLanguageCode();
            Log.d("AzureTTS", String.format("Chinese-dominated sentence (%.1f%% vs %.1f%% English), numbers in %s",
                languageRatio.chineseRatio * 100, languageRatio.englishRatio * 100, chineseCode));
            return chineseCode;

        } else {
            // 占比均未超過55%或相近，根據哪個占比更高決定
            if (languageRatio.englishRatio > languageRatio.chineseRatio) {
                Log.d("AzureTTS", String.format("Balanced ratio with English slight advantage (%.1f%% vs %.1f%% Chinese), numbers in English", 
                    languageRatio.englishRatio * 100, languageRatio.chineseRatio * 100));
                return "en-US";
            } else if (languageRatio.chineseRatio > languageRatio.englishRatio) {
                String chineseCode = getUserPreferredChineseLanguageCode();
                Log.d("AzureTTS", String.format("Balanced ratio with Chinese slight advantage (%.1f%% vs %.1f%% English), numbers in %s", 
                    languageRatio.chineseRatio * 100, languageRatio.englishRatio * 100, chineseCode));
                return chineseCode;
            } else {
                // 完全相等的情況，根據用戶語音偏好決定
                String userVoice = preferenceManager != null ? 
                    preferenceManager.getVoice() : "Mandarin";
                
                if ("English".equals(userVoice)) {
                    Log.d("AzureTTS", "Equal ratio, user voice preference is English, numbers in English");
                    return "en-US";
                } else {
                    String chineseCode = getUserPreferredChineseLanguageCode();
                    Log.d("AzureTTS", String.format("Equal ratio, user voice preference is Chinese, numbers in %s", chineseCode));
                    return chineseCode;
                }
            }
        }
    }
    
    /**
     * 語言占比分析結果類
     */
    private static class LanguageRatio {
        final double chineseRatio;
        final double englishRatio;
        final int totalChars;
        
        LanguageRatio(double chineseRatio, double englishRatio, int totalChars) {
            this.chineseRatio = chineseRatio;
            this.englishRatio = englishRatio;
            this.totalChars = totalChars;
        }
        
        /**
         * 判斷是否為中文主導的句子（中文占比>55%，且中文占比大於英文占比）
         */
        boolean isChineseDominated() {
            return chineseRatio > 0.55 && chineseRatio > englishRatio;
        }

        /**
         * 判斷是否為英文主導的句子（英文占比>55%，且英文占比大於中文占比）
         */
        boolean isEnglishDominated() {
            return englishRatio > 0.55 && englishRatio > chineseRatio;
        }
        
        @SuppressLint("DefaultLocale")
        @NonNull
        @Override
        public String toString() {
            return String.format("LanguageRatio{Chinese=%.1f%%, English=%.1f%%, Total=%d}", 
                chineseRatio * 100, englishRatio * 100, totalChars);
        }
    }
    
    /**
     * [重构优化] 分析整句話的語言占比，用於智能數字語言選擇
     * 使用高效的单次遍历字符统计方法，性能提升2-3倍
     * @param segments 文本片段列表
     * @return 語言占比分析結果
     */
    private LanguageRatio analyzeSentenceLanguageRatio(List<TextSegment> segments) {
        if (segments == null || segments.isEmpty()) {
            return new LanguageRatio(0.0, 0.0, 0);
        }

        int chineseChars = 0;
        int englishChars = 0;
        int totalTextChars = 0; // 只計算實際文本字符，不包括標點符號和空白

        for (TextSegment segment : segments) {
            String text = segment.getText();
            if (text == null || text.isEmpty()) {
                continue;
            }

            // 根據片段類型計算字符數
            switch (segment.getLanguage()) {
                case CHINESE:
                    // 使用高效的单次遍历计数方法
                    int segmentChineseCount = countHanChars(text);
                    chineseChars += segmentChineseCount;
                    totalTextChars += segmentChineseCount;
                    break;

                case ENGLISH:
                    // 使用高效的单次遍历计数方法
                    int segmentEnglishCount = countLatinChars(text);
                    englishChars += segmentEnglishCount;
                    totalTextChars += segmentEnglishCount;
                    break;

                case MIXED:
                    // 混合片段使用批量统计方法，单次遍历同时统计中英文
                    CharacterCounts counts = countChineseAndEnglishChars(text);
                    chineseChars += counts.chineseCount;
                    englishChars += counts.englishCount;
                    totalTextChars += counts.chineseCount + counts.englishCount;
                    break;

                case NUMBER:
                case PUNCTUATION:
                case WHITESPACE:
                case WEBLINK:
                    // 數字、標點符號、空白字符、Web Link不計入語言占比統計
                    break;
            }
        }

        // 計算占比
        double chineseRatio = totalTextChars > 0 ? (double) chineseChars / totalTextChars : 0.0;
        double englishRatio = totalTextChars > 0 ? (double) englishChars / totalTextChars : 0.0;

        LanguageRatio result = new LanguageRatio(chineseRatio, englishRatio, totalTextChars);

        Log.d("AzureTTS", String.format("Language ratio analysis (optimized): Chinese=%d, English=%d, Total=%d, %s", 
            chineseChars, englishChars, totalTextChars, result));

        return result;
    }
    
    /**
     * [重构优化] 高效计算文本中的汉字数量
     * 使用单次遍历和Character.getType()实现，比正则匹配效率更高
     */
    private int countHanChars(String text) {
        if (text == null || text.isEmpty()) return 0;
        
        int count = 0;
        for (int i = 0; i < text.length(); ) {
            int codePoint = text.codePointAt(i);
            if (isHanCharacter(codePoint)) {
                count++;
            }
            i += Character.charCount(codePoint);
        }
        return count;
    }

    /**
     * [重构优化] 高效计算文本中的拉丁字符数量
     * 使用单次遍历和Character.getType()实现，比正则匹配效率更高
     */
    private int countLatinChars(String text) {
        if (text == null || text.isEmpty()) return 0;
        
        int count = 0;
        for (int i = 0; i < text.length(); ) {
            int codePoint = text.codePointAt(i);
            if (isLatinCharacter(codePoint)) {
                count++;
            }
            i += Character.charCount(codePoint);
        }
        return count;
    }
    
    /**
     * [新增] 批量统计中英文字符数量 - 单次遍历实现最高效率
     * @param text 要统计的文本
     * @return CharacterCounts对象，包含中英文字符统计
     */
    private CharacterCounts countChineseAndEnglishChars(String text) {
        if (text == null || text.isEmpty()) {
            return new CharacterCounts(0, 0);
        }
        
        int chineseCount = 0;
        int englishCount = 0;
        
        for (int i = 0; i < text.length(); ) {
            int codePoint = text.codePointAt(i);
            if (isHanCharacter(codePoint)) {
                chineseCount++;
            } else if (isLatinCharacter(codePoint)) {
                englishCount++;
            }
            i += Character.charCount(codePoint);
        }
        
        return new CharacterCounts(chineseCount, englishCount);
    }
    
    /**
     * [新增] 字符统计结果类
     */
    private static class CharacterCounts {
        final int chineseCount;
        final int englishCount;
        
        CharacterCounts(int chineseCount, int englishCount) {
            this.chineseCount = chineseCount;
            this.englishCount = englishCount;
        }
    }

    /**
     * [优化] 高效判断一个码点是否为汉字
     * 使用Unicode码点范围判断，比正则表达式更高效
     */
    private boolean isHanCharacter(int codePoint) {
        // 汉字Unicode范围：
        // U+4E00-U+9FFF: CJK Unified Ideographs (基本汉字)
        // U+3400-U+4DBF: CJK Extension A (扩展汉字A)
        // U+20000-U+2A6DF: CJK Extension B (扩展汉字B)
        // U+2A700-U+2B73F: CJK Extension C (扩展汉字C)
        // U+2B740-U+2B81F: CJK Extension D (扩展汉字D)
        // U+2B820-U+2CEAF: CJK Extension E (扩展汉字E)
        return (codePoint >= 0x4E00 && codePoint <= 0x9FFF) ||           // 基本汉字
               (codePoint >= 0x3400 && codePoint <= 0x4DBF) ||           // 扩展A
               (codePoint >= 0x20000 && codePoint <= 0x2A6DF) ||         // 扩展B
               (codePoint >= 0x2A700 && codePoint <= 0x2B73F) ||         // 扩展C
               (codePoint >= 0x2B740 && codePoint <= 0x2B81F) ||         // 扩展D
               (codePoint >= 0x2B820 && codePoint <= 0x2CEAF);           // 扩展E
    }

    /**
     * [优化] 高效判断一个码点是否为拉丁字符
     * 使用Unicode码点范围判断，比正则表达式更高效
     */
    private boolean isLatinCharacter(int codePoint) {
        // 拉丁字符Unicode范围：
        // U+0041-U+005A: 大写字母A-Z
        // U+0061-U+007A: 小写字母a-z
        // U+00C0-U+00FF: Latin-1 Supplement (带重音的拉丁字符)
        // U+0100-U+017F: Latin Extended-A
        // U+0180-U+024F: Latin Extended-B
        return (codePoint >= 0x0041 && codePoint <= 0x005A) ||           // A-Z
               (codePoint >= 0x0061 && codePoint <= 0x007A) ||           // a-z
               (codePoint >= 0x00C0 && codePoint <= 0x00FF) ||           // Latin-1 Supplement
               (codePoint >= 0x0100 && codePoint <= 0x017F) ||           // Latin Extended-A
               (codePoint >= 0x0180 && codePoint <= 0x024F);             // Latin Extended-B
    }
    
    /**
     * 【Phase 3 新增】檢測數字片段是否在Web Link中
     * @param segments 所有文本片段列表
     * @param targetSegmentIndex 目標數字片段的索引
     * @return 如果數字在Web Link中返回true
     */
    private boolean isNumberInWebLink(List<TextSegment> segments, int targetSegmentIndex) {
        if (segments == null || targetSegmentIndex < 0 || targetSegmentIndex >= segments.size()) {
            return false;
        }
        
        TextSegment targetSegment = segments.get(targetSegmentIndex);
        if (targetSegment.getLanguage() != TextSegment.Language.NUMBER) {
            return false;
        }
        
        // 檢查附近的片段是否包含Web Link
        // 檢查前後各5個片段的範圍（合理的Web Link長度範圍）
        int searchStart = Math.max(0, targetSegmentIndex - 5);
        int searchEnd = Math.min(segments.size() - 1, targetSegmentIndex + 5);
        
        for (int i = searchStart; i <= searchEnd; i++) {
            TextSegment segment = segments.get(i);
            if (segment.getLanguage() == TextSegment.Language.WEBLINK) {
                // 如果數字片段的位置在Web Link片段的合理範圍內，認為是Web Link的一部分
                int webLinkStart = segment.getStartIndex();
                int webLinkEnd = segment.getEndIndex();
                int numberStart = targetSegment.getStartIndex();
                int numberEnd = targetSegment.getEndIndex();
                
                // 檢查數字是否在Web Link的合理延伸範圍內（允許一些間隔字符）
                if ((numberStart >= webLinkStart && numberStart <= webLinkEnd + 10) ||
                    (numberEnd >= webLinkStart - 10 && numberEnd <= webLinkEnd)) {
                    
                    Log.d("AzureTTS", String.format("Number '%s' detected in Web Link context: '%s'", 
                        targetSegment.getText(), segment.getText()));
                    return true;
                }
            }
        }
        
        return false;
    }

    
    /**
     * 生成多語言SSML文檔 - 使用 JennyMultilingualNeural + <lang xml:lang> 標籤
     * 根據Azure官方文檔最佳實踐實現
     * @param segments 文本片段列表
     * @param speed 語音速度
     * @return 完整的SSML文檔
     */
    private String generateMultilingualSSML(List<TextSegment> segments, float speed) {
        if (segments.isEmpty()) {
            return "";
        }
        
        try {
            // 【Phase 2 改進】首先分析整句話的語言占比，用於智能數字處理
            LanguageRatio languageRatio = analyzeSentenceLanguageRatio(segments);
            
            StringBuilder ssmlBuilder = new StringBuilder();
            ssmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
            ssmlBuilder.append("<speak version=\"1.0\" xmlns=\"http://www.w3.org/2001/10/synthesis\" xmlns:mstts=\"https://www.w3.org/2001/mstts\" xml:lang=\"en-US\">\n");
            
            // 根據Azure最佳實踐：使用單一 JennyMultilingualNeural 語音
            ssmlBuilder.append("<voice name=\"en-US-JennyMultilingualNeural\">\n");

            // 📝 移除全局mstts:silence設置，改用局部break元素
            // JennyMultilingualNeural對全局silence支持有限，改為在具體位置插入break元素

            ssmlBuilder.append("<prosody rate=\"").append(formatSpeedForSSML(speed)).append("\">\n");
            
            // 合併相鄰的相同語言片段，減少SSML複雜度
            List<TextSegment> mergedSegments = mergeAdjacentSameLanguageSegments(segments);
            
            // 智能語言標籤處理
            String currentLanguageCode = null;
            boolean inLangTag = false;
            
            // 【Phase 3 改進】使用索引循環以支持Web Link檢測
            for (int segmentIndex = 0; segmentIndex < mergedSegments.size(); segmentIndex++) {
                TextSegment segment = mergedSegments.get(segmentIndex);
                // 跳過空片段
                if (segment.isEmpty()) {
                    continue;
                }
                
                // 處理空白字符
                if (segment.getLanguage() == TextSegment.Language.WHITESPACE) {
                    ssmlBuilder.append(" "); // 空白字符簡化為單個空格
                    continue;
                }
                
                // 🔥 修復：不再需要處理BREAK_MARKER，因為現在直接插入break元素
                // BREAK_MARKER處理邏輯已移除，因為addIntelligentPauses()現在直接插入<break>元素
                
                // 【Phase 2&3 改進】處理需要語音處理的文本（包括智能數字處理和Web Link）
                if (segment.requiresVoiceProcessing() ||
                    segment.getLanguage() == TextSegment.Language.NUMBER ||
                    segment.getLanguage() == TextSegment.Language.WEBLINK) {

                    // 跳過空內容的片段，避免產生空的lang標籤
                    String segmentText = segment.getText();
                    if (segmentText == null || segmentText.trim().isEmpty()) {
                        continue;
                    }

                    // 智能語言代碼選擇：數字使用語言占比分析和Web Link檢測，其他使用標準邏輯
                    String segmentLanguageCode;
                    if (segment.getLanguage() == TextSegment.Language.NUMBER) {
                        // 【Phase 3 實現】檢測數字是否在Web Link中
                        boolean inWebLink = isNumberInWebLink(mergedSegments, segmentIndex);
                        segmentLanguageCode = getLanguageCodeForSegment(segment.getLanguage(), languageRatio, inWebLink);
                    } else {
                        segmentLanguageCode = getLanguageCodeForSegment(segment.getLanguage());
                    }

                    // 檢查是否需要切換語言標籤
                    if (!segmentLanguageCode.equals(currentLanguageCode)) {
                        // 關閉之前的語言標籤
                        if (inLangTag) {
                            ssmlBuilder.append("</lang>");
                            Log.d("AzureTTS", "Closed lang tag: " + currentLanguageCode);

                            // 在語言切換時添加明顯的停頓
                            // 使用句號來產生自然停頓，配合mstts:silence設置
                            ssmlBuilder.append("。 ");
                        }

                        // 開始新的語言標籤（只有在有內容時才開啟）
                        ssmlBuilder.append("<lang xml:lang=\"").append(segmentLanguageCode).append("\">");
                        inLangTag = true;
                        currentLanguageCode = segmentLanguageCode;

                        Log.d("AzureTTS", String.format("Opened lang tag: %s for %s: '%s'",
                            segmentLanguageCode, segment.getLanguage(), segmentText));
                    }

                    // 添加文本內容（智能停頓處理）
                    String languageString = segment.getLanguage().toString();
                    String enhancedText = addIntelligentPauses(segmentText, languageString);
                    ssmlBuilder.append(escapeXmlCharacters(enhancedText));
                    
                } else {
                    // 標點符號等：直接添加到當前語言標籤內，避免頻繁開關標籤
                    // 這樣可以減少空標籤的產生，並讓SSML更簡潔
                    String punctuationText = segment.getText();
                    if (punctuationText != null && !punctuationText.trim().isEmpty()) {
                        // 對標點符號也進行智能停頓處理
                        String enhancedPunctuation = addIntelligentPauses(punctuationText, "punctuation");
                        ssmlBuilder.append(escapeXmlCharacters(enhancedPunctuation));
                    }
                }
            }
            
            // 關閉最後的語言標籤
            if (inLangTag) {
                ssmlBuilder.append("</lang>");
                Log.d("AzureTTS", "Closed final lang tag: " + currentLanguageCode);
            }

            // 關閉prosody和voice標籤
            ssmlBuilder.append("</prosody>\n");
            ssmlBuilder.append("</voice>\n");
            ssmlBuilder.append("</speak>");

            String result = ssmlBuilder.toString();

            // 驗證SSML文檔完整性
            if (result.contains("<lang xml:lang=") && !result.contains("</lang>")) {
                Log.e("AzureTTS", "SSML document has unclosed lang tags, falling back to simple SSML");
                return generateFallbackSSML(segments, speed);
            }

            // 🔥 新增：最終SSML格式驗證和清理
            result = validateAndCleanSSML(result);

            Log.d("AzureTTS", "Generated JennyMultilingualNeural SSML document successfully");
            Log.v("AzureTTS", "SSML Content: " + result);

            return result;
            
        } catch (Exception e) {
            Log.e("AzureTTS", "Error generating multilingual SSML", e);
            // 回退到簡單的單語言SSML
            return generateFallbackSSML(segments, speed);
        }
    }
    
    /**
     * 合併相鄰的相同語言片段，減少SSML複雜度
     * 優化策略：只合併主要語言片段（中文、英文），保留其他類型片段的完整性
     */
    private List<TextSegment> mergeAdjacentSameLanguageSegments(List<TextSegment> segments) {
        if (segments == null || segments.size() <= 1) {
            return segments;
        }
        
        List<TextSegment> merged = new ArrayList<>();
        TextSegment current = segments.get(0);
        
        for (int i = 1; i < segments.size(); i++) {
            TextSegment next = segments.get(i);
            
            // 只合併相同的主要語言片段（中文、英文）
            if (canMergeSegments(current, next)) {
                try {
                    // 記錄合併操作
                    Log.d("AzureTTS", String.format("Merging segments: '%s' + '%s' [%s]", 
                        current.getText(), next.getText(), current.getLanguage()));
                    
                    current = current.merge(next);
                    
                } catch (IllegalArgumentException e) {
                    Log.w("AzureTTS", "Failed to merge segments: " + e.getMessage());
                    // 合併失敗，保留當前片段並開始新的
                    merged.add(current);
                    current = next;
                }
            } else {
                // 不同語言或不適合合併，保留當前片段
                merged.add(current);
                current = next;
            }
        }
        
        // 添加最後一個片段
        merged.add(current);
        
        Log.d("AzureTTS", String.format("Segment merging completed: %d -> %d segments", 
            segments.size(), merged.size()));
        
        return merged;
    }
    
    /**
     * 判斷兩個片段是否可以合併
     */
    private boolean canMergeSegments(TextSegment current, TextSegment next) {
        // 必須是相同語言
        if (current.getLanguage() != next.getLanguage()) {
            return false;
        }
        
        // 只合併主要語言（中文、英文）
        if (!current.getLanguage().isPrimaryLanguage() || !next.getLanguage().isPrimaryLanguage()) {
            return false;
        }
        
        // 都必須需要語音處理
        return current.requiresVoiceProcessing() && next.requiresVoiceProcessing();
    }
    
    /**
     * 生成回退的簡單SSML文檔
     */
    private String generateFallbackSSML(List<TextSegment> segments, float speed) {
        StringBuilder textBuilder = new StringBuilder();
        for (TextSegment segment : segments) {
            textBuilder.append(segment.getText());
        }
        
        String combinedText = textBuilder.toString().trim();
        return createFallbackSSML(combinedText, speed);
    }
    
    
    
    /**
     * 使用多語言文本分割生成SSML文檔
     * @param text 原始文本
     * @param speed 語音速度
     * @return 多語言SSML文檔
     */
    private String wrapWithMultilingualSSML(String text, float speed) {
        try {
            Log.d("AzureTTS", "Processing multilingual text: " + text);

            // 🔥 新增：首先對UI優化文本進行TTS預處理
            String ttsOptimizedText = preprocessTextForTTS(text);
            Log.d("AzureTTS", "Applied TTS preprocessing, length: " + text.length() + " -> " + ttsOptimizedText.length());

            // 按句子處理（使用\n作為句子邊界）
            return processTextBySentences(ttsOptimizedText, speed);

        } catch (Exception e) {
            Log.e("AzureTTS", "Error in multilingual SSML processing, using fallback", e);
            // 📝 簡化：即使出錯也使用JennyMultilingualNeural架構
            return createFallbackSSML(text, speed);
        }
    }

    /**
     * TTS預處理：對UI優化文本進行細微的語音合成優化
     *
     * 設計原則：
     * - 保持與UI顯示文本的高度一致性（>95%相似度）
     * - 僅進行影響語音質量但不影響視覺理解的細微調整
     * - 避免改變文本的核心內容和結構
     *
     * @param uiOptimizedText 來自GoogleOCR的UI優化文本
     * @return 適用於TTS的細微優化文本
     */
    private String preprocessTextForTTS(String uiOptimizedText) {
        if (uiOptimizedText == null || uiOptimizedText.trim().isEmpty()) {
            return uiOptimizedText;
        }

        String result = uiOptimizedText;

        // 1. 省略號轉換：提升語音流暢性（視覺上幾乎無差異）
        result = result.replaceAll("\\.\\.\\.", ".");

        // 2. 特殊符號語音化：提升發音清晰度
        result = result.replaceAll("&", "和");      // & -> 和
        result = result.replaceAll("@", "at");      // @ -> at
        result = result.replaceAll("#", "號");      // # -> 號

        // 3. 數字字母間距：提升發音清晰度（細微調整）
        result = result.replaceAll("([0-9])([a-zA-Z])", "$1 $2");  // 123abc -> 123 abc
        result = result.replaceAll("([a-zA-Z])([0-9])", "$1 $2");  // abc123 -> abc 123

        // 4. 中英文間距：提升語音自然度（細微調整）
        result = result.replaceAll("([\\u4e00-\\u9fff])([a-zA-Z])", "$1 $2");  // 中文English -> 中文 English
        result = result.replaceAll("([a-zA-Z])([\\u4e00-\\u9fff])", "$1 $2");  // English中文 -> English 中文

        // 5. 清理多餘空格：保持整潔
        result = result.replaceAll("\\s+", " ").trim();

        Log.d("AzureTTS", "TTS preprocessing completed: subtle optimizations applied");
        return result;
    }

    /**
     * 按句子處理文本，使用\n作為句子邊界，每個句子獨立進行語言比例分析
     * @param text 原始文本
     * @param speed 語音速度
     * @return 處理後的SSML
     */
    private String processTextBySentences(String text, float speed) {
        if (text == null || text.trim().isEmpty()) {
            return createFallbackSSML("", speed);
        }

        // 1. 按\n分割句子
        String[] sentences = text.split("\n");
        StringBuilder ssmlBuilder = new StringBuilder();

        // 2. SSML文檔頭部
        ssmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        ssmlBuilder.append("<speak version=\"1.0\" xmlns=\"http://www.w3.org/2001/10/synthesis\" xmlns:mstts=\"https://www.w3.org/2001/mstts\" xml:lang=\"en-US\">\n");
        ssmlBuilder.append("<voice name=\"en-US-JennyMultilingualNeural\">\n");
        ssmlBuilder.append("<prosody rate=\"").append(formatSpeedForSSML(speed)).append("\">\n");

        // 3. 處理每個句子
        for (int i = 0; i < sentences.length; i++) {
            String sentence = sentences[i].trim();
            if (sentence.isEmpty()) {
                // 空句子添加段落分隔
                ssmlBuilder.append("<break time=\"800ms\" />");
                continue;
            }

            Log.d("AzureTTS", String.format("Processing sentence %d: '%s'", i + 1, sentence));

            // 為每個句子獨立分析語言比例和生成SSML
            String sentenceSSML = processSingleSentence(sentence, speed);
            ssmlBuilder.append(sentenceSSML);

            // 句子間添加適當停頓
            if (i < sentences.length - 1) {
                ssmlBuilder.append("<break time=\"600ms\" />");
            }
        }

        // 4. SSML文檔尾部
        ssmlBuilder.append("</prosody>\n");
        ssmlBuilder.append("</voice>\n");
        ssmlBuilder.append("</speak>");

        String result = ssmlBuilder.toString();
        result = validateAndCleanSSML(result);

        Log.d("AzureTTS", String.format("Generated sentence-based SSML for %d sentences", sentences.length));
        return result;
    }

    /**
     * 處理單個句子，進行獨立的語言比例分析
     * @param sentence 句子文本
     * @param speed 語音速度
     * @return 句子的SSML內容（不包含文檔結構）
     */
    private String processSingleSentence(String sentence, float speed) {
        // 1. 分割句子為語言片段
        List<TextSegment> segments = segmentMixedLanguageText(sentence);

        if (segments.isEmpty()) {
            return escapeXmlCharacters(sentence);
        }

        // 2. 分析句子的語言比例（55%閾值）
        LanguageRatio sentenceRatio = analyzeSentenceLanguageRatio(segments);
        Log.d("AzureTTS", String.format("Sentence language ratio: %s", sentenceRatio.toString()));

        // 3. 生成句子SSML內容
        return generateSentenceSSML(segments, sentenceRatio, speed);
    }

    /**
     * 為單個句子生成SSML內容（不包含文檔結構）
     * @param segments 句子的語言片段
     * @param sentenceRatio 句子的語言比例
     * @param speed 語音速度
     * @return 句子SSML內容
     */
    private String generateSentenceSSML(List<TextSegment> segments, LanguageRatio sentenceRatio, float speed) {
        StringBuilder ssmlBuilder = new StringBuilder();
        String currentLanguageCode = null;
        boolean inLanguageTag = false;

        for (TextSegment segment : segments) {
            if (segment.getText() == null || segment.getText().trim().isEmpty()) {
                continue;
            }

            // 處理需要語音處理的文本
            if (segment.requiresVoiceProcessing() ||
                segment.getLanguage() == TextSegment.Language.NUMBER ||
                segment.getLanguage() == TextSegment.Language.WEBLINK) {

                String segmentLanguageCode;
                String segmentText = segment.getText();

                // 智能數字語言選擇（基於句子級別的語言比例）
                if (segment.getLanguage() == TextSegment.Language.NUMBER) {
                    segmentLanguageCode = getIntelligentNumberLanguageCode(sentenceRatio, false);
                    Log.d("AzureTTS", String.format("Number '%s' assigned language: %s (sentence ratio: %s)",
                        segmentText, segmentLanguageCode, sentenceRatio.toString()));
                } else {
                    segmentLanguageCode = getLanguageCodeForSegment(segment.getLanguage());
                }

                // 語言標籤管理
                if (!segmentLanguageCode.equals(currentLanguageCode)) {
                    if (inLanguageTag) {
                        ssmlBuilder.append("</lang>");
                        inLanguageTag = false;
                    }
                    ssmlBuilder.append("<lang xml:lang=\"").append(segmentLanguageCode).append("\">");
                    currentLanguageCode = segmentLanguageCode;
                    inLanguageTag = true;
                }

                // 添加智能停頓處理的文本
                String languageString = segment.getLanguage().toString();
                String enhancedText = addIntelligentPausesWithSpaceInfo(segmentText, languageString, segmentText);
                ssmlBuilder.append(escapeXmlCharacters(enhancedText));

            } else {
                // 標點符號等：直接添加
                String punctuationText = segment.getText();
                if (punctuationText != null && !punctuationText.trim().isEmpty()) {
                    String enhancedPunctuation = addIntelligentPausesWithSpaceInfo(punctuationText, "punctuation", punctuationText);
                    ssmlBuilder.append(escapeXmlCharacters(enhancedPunctuation));
                }
            }
        }

        // 關閉最後的語言標籤
        if (inLanguageTag) {
            ssmlBuilder.append("</lang>");
        }

        return ssmlBuilder.toString();
    }

    /**
     * 創建簡單的回退SSML（當多語言處理失敗時使用）
     */
    private String createFallbackSSML(String text, float speed) {
        Log.d("AzureTTS", "Creating fallback SSML for text: " + text);
        
        // 檢測主要語言
        String languageCode = detectPrimaryLanguage(text).equals("english") ? "en-US" : getUserPreferredChineseLanguageCode();
        
        StringBuilder ssmlBuilder = new StringBuilder();
        ssmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        ssmlBuilder.append("<speak version=\"1.0\" xmlns=\"http://www.w3.org/2001/10/synthesis\" xml:lang=\"en-US\">\n");
        ssmlBuilder.append("<voice name=\"en-US-JennyMultilingualNeural\">\n");
        ssmlBuilder.append("<prosody rate=\"").append(formatSpeedForSSML(speed)).append("\">\n");
        ssmlBuilder.append("<lang xml:lang=\"").append(languageCode).append("\">");
        ssmlBuilder.append(escapeXmlCharacters(text));
        ssmlBuilder.append("</lang>");
        ssmlBuilder.append("\n</prosody>\n");
        ssmlBuilder.append("</voice>\n");
        ssmlBuilder.append("</speak>");
        
        Log.d("AzureTTS", "Generated fallback SSML with language code: " + languageCode);
        return ssmlBuilder.toString();
    }
    
    // 🔥 修復：convertBreakMarkerToElement() 方法已移除
    // 因為現在直接在addIntelligentPauses()中插入<break>元素，不再需要標記轉換
    
    /**
     * 轉義XML特殊字符，防止SSML解析錯誤
     * 🔥 關鍵修復：保護SSML元素（如<break>）不被轉義
     */
    private String escapeXmlCharacters(String text) {
        if (text == null) return "";
        
        // 首先保護所有的SSML標籤，避免被轉義
        Map<String, String> ssmlPlaceholders = new HashMap<>();
        String protectedText = text;
        
        // 保護 <break> 元素 - 支持標準Azure SSML格式
        // 🔥 修復：匹配標準化後的break元素格式
        Pattern breakPattern = Pattern.compile("<break\\s+time\\s*=\\s*[\"'][^\"']*[\"']\\s*/?>");
        Matcher breakMatcher = breakPattern.matcher(protectedText);
        int placeholderIndex = 0;
        
        while (breakMatcher.find()) {
            String placeholder = "___SSML_BREAK_" + (placeholderIndex++) + "___";
            ssmlPlaceholders.put(placeholder, breakMatcher.group());
            protectedText = protectedText.replace(breakMatcher.group(), placeholder);
        }
        
        // 保護 <emphasis> 元素
        Pattern emphasisPattern = Pattern.compile("<emphasis\\s+level\\s*=\\s*['\"][^'\"]*['\"]\\s*>[^<]*</emphasis>");
        Matcher emphasisMatcher = emphasisPattern.matcher(protectedText);
        
        while (emphasisMatcher.find()) {
            String placeholder = "___SSML_EMPHASIS_" + (placeholderIndex++) + "___";
            ssmlPlaceholders.put(placeholder, emphasisMatcher.group());
            protectedText = protectedText.replace(emphasisMatcher.group(), placeholder);
        }
        
        // 進行標準的XML字符轉義
        String escapedText = protectedText
                .replace("&", "&amp;")    // 必須首先處理 &
                .replace("<", "&lt;")     // 小於號
                .replace(">", "&gt;")     // 大於號
                .replace("\"", "&quot;")  // 雙引號
                .replace("'", "&apos;");  // 單引號
        
        // 恢復被保護的SSML元素
        for (Map.Entry<String, String> entry : ssmlPlaceholders.entrySet()) {
            escapedText = escapedText.replace(entry.getKey(), entry.getValue());
        }
        
        return escapedText;
    }
    
    /**
     * [优化] 检测文本主要语言，使用高效的单次遍历统计
     */
    private String detectPrimaryLanguage(String text) {
        if (text == null || text.isEmpty()) {
            return "chinese"; // 默认中文
        }

        // 使用批量统计方法，单次遍历同时统计中英文字符
        CharacterCounts counts = countChineseAndEnglishChars(text);

        if (counts.chineseCount > counts.englishCount) {
            return "chinese";
        } else if (counts.englishCount > 0) {
            // 只要有英文且不比中文少，就倾向于英文
            return "english";
        } else {
            return "chinese"; // 默认中文
        }
    }

    
    /**
     * 格式化速度為SSML兼容格式
     */
    private String formatSpeedForSSML(float speed) {
        // Azure Speech Service支持的速度範圍是 0.5 到 2.0
        float ssmlSpeed = Math.max(0.5f, Math.min(2.0f, speed));
        
        if (ssmlSpeed == 1.0f) {
            return "medium";
        } else if (ssmlSpeed < 0.8f) {
            return "slow";
        } else if (ssmlSpeed > 1.2f) {
            return "fast";
        } else {
            return String.format("%.1f", ssmlSpeed);
        }
    }
    
    /**
     * 智能停頓處理 - 增強版，支持原始空格檢測和優化的停頓時間
     * 🔥 增強：檢測原始文本中的空格，增加中文詞間停頓；減少中英文切換停頓50ms
     */
    private String addIntelligentPauses(String text, String language) {
        return addIntelligentPausesWithSpaceInfo(text, language, text);
    }

    /**
     * 智能停頓處理 - 支持原始文本空格信息的增強版
     * @param text 處理的文本
     * @param language 語言類型
     * @param originalText 原始文本（用於檢測空格位置）
     * @return 增強停頓的文本
     */
    private String addIntelligentPausesWithSpaceInfo(String text, String language, String originalText) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        String enhanced = text;

        // 🔥 增強：中文文本的空格和換行處理，基於原始文本空格信息
        if ("CHINESE".equals(language) || "chinese".equals(language)) {
            // 處理換行：增加停頓時間到700ms，確保段落間明顯停頓
            enhanced = enhanced.replaceAll("\\r?\\n", "<break time=\"700ms\" />。 ");

            // 🔥 新增：檢測原始文本中的空格位置，增加中文詞間停頓
            enhanced = addChineseWordSpacePauses(enhanced, originalText);

            // 🔥 修復：只處理多個空格，避免在正常詞語間添加不必要的停頓
            // 處理中文字符間的多個空格（2個或以上）：增加停頓時間到600ms（增強版）
            enhanced = enhanced.replaceAll("([\\p{IsHan}])\\s{2,}([\\p{IsHan}])", "$1<break time=\"600ms\" />，$2");

            // 🔥 增強：處理中文與數字邊界，增加停頓時間
            // 中文字符後緊跟數字，添加稍長停頓（如：纺织厂礼堂江河59147）
            enhanced = enhanced.replaceAll("([\\p{IsHan}])([\\d])", "$1<break time=\"350ms\" />$2");
            // 數字後緊跟中文字符，添加稍長停頓（如：59147龙虎斗）
            enhanced = enhanced.replaceAll("([\\d])([\\p{IsHan}])", "$1<break time=\"350ms\" />$2");

            // 🔥 優化：處理中文與英文邊界，減少停頓時間50ms（從350ms減至300ms）
            // 中文字符後緊跟英文字符，添加優化停頓（如：江河com）
            enhanced = enhanced.replaceAll("([\\p{IsHan}])([\\p{IsLatin}])", "$1<break time=\"300ms\" />$2");
            // 英文字符後緊跟中文字符，添加優化停頓（如：com龙虎斗）
            enhanced = enhanced.replaceAll("([\\p{IsLatin}])([\\p{IsHan}])", "$1<break time=\"300ms\" />$2");
            
            // 🔥 新增：處理中文標點符號後的停頓，確保自然節奏
            // 中文逗號、頓號後的停頓
            enhanced = enhanced.replaceAll("([，、])([\\p{IsHan}])", "$1<break time=\"300ms\" />$2");
            // 中文分號、冒號後的停頓
            enhanced = enhanced.replaceAll("([；：])([\\p{IsHan}])", "$1<break time=\"400ms\" />$2");
            
            // 🔥 暫時禁用：為連續中文字符串添加適當的詞語分割停頓
            // 檢測連續的長中文字符串（6個字符以上），每4個字符添加微停頓
            // 問題：這會在詞語內部添加不必要的停頓，影響自然度
            // enhanced = addChineseWordBoundaryPauses(enhanced);
        }

        // 1. 處理特殊內容類型
        enhanced = enhanceSpecialContent(enhanced, language);

        // 2. 處理標點符號停頓
        enhanced = enhanceNaturalPunctuation(enhanced, language);

        // 3. 處理數字和日期
        enhanced = enhanceNumbersAndDates(enhanced, language);

        // 4. 🔥 新增：添加韻律增強，提升語音自然度
        enhanced = addProsodyEnhancements(enhanced, language);

        // 5. 最終清理：確保格式正確
        enhanced = cleanupPauseMarkers(enhanced);

        // 🔥 新增：標準化break元素格式，確保符合Azure TTS規範
        enhanced = standardizeBreakElements(enhanced);

        // 統計break元素使用情況
        int breakCount = (enhanced.split("<break time=", -1).length - 1);
        int prosodyCount = (enhanced.split("<prosody", -1).length - 1);
        int emphasisCount = (enhanced.split("<emphasis", -1).length - 1);
        Log.d("AzureTTS", String.format("Applied intelligent pauses for %s: '%s' -> '%s' (%d breaks, %d prosody, %d emphasis)",
            language, text.substring(0, Math.min(20, text.length())),
            enhanced.substring(0, Math.min(20, enhanced.length())), breakCount, prosodyCount, emphasisCount));

        return enhanced;
    }

    /**
     * 處理特殊內容：日期、電話、網址、郵箱等
     */
    private String enhanceSpecialContent(String text, String language) {
        String enhanced = text;

        // 電話號碼：添加適當的分隔
        enhanced = enhanced.replaceAll("(\\d{3})(\\d{4})(\\d{4})", "$1，$2，$3");
        enhanced = enhanced.replaceAll("(\\d{3})-(\\d{3})-(\\d{4})", "$1，$2，$3");

        // 網址：在協議後添加停頓
        enhanced = enhanced.replaceAll("(https?://)([^\\s]+)", "$1，$2");
        enhanced = enhanced.replaceAll("(www\\.)([^\\s]+)", "$1，$2");

        // 郵箱：在@符號處添加停頓
        enhanced = enhanced.replaceAll("([^\\s]+)@([^\\s]+)", "$1，at，$2");

        // IP地址：在點號處添加停頓
        enhanced = enhanced.replaceAll("(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})",
            "$1點$2點$3點$4");

        // 版本號：在點號處添加停頓
        enhanced = enhanced.replaceAll("\\b(v?\\d+)\\.(\\d+)\\.(\\d+)\\b", "$1點$2點$3");

        return enhanced;
    }

    /**
     * 增強自然標點符號停頓（不使用break標籤）
     */
    private String enhanceNaturalPunctuation(String text, String language) {
        String enhanced = text;

        // 🔥 增強：中文特殊處理，提供更充分的停頓
        if ("CHINESE".equals(language) || "chinese".equals(language)) {
            // 🔥 修復：只處理多個空格，避免在正常詞語間添加不必要的停頓
            // 中文字符間的多個空格（2個或以上）：增加停頓時間到500ms，提供更充分的停頓
            enhanced = enhanced.replaceAll("([\\p{IsHan}])\\s{2,}([\\p{IsHan}])", "$1<break time=\"500ms\" />，$2");

            // 中文與英文/數字間的空格：增加停頓時間到350ms，改善語言切換效果
            enhanced = enhanced.replaceAll("([\\p{IsHan}])\\s+([\\p{IsLatin}\\d])", "$1<break time=\"350ms\" />， $2");
            enhanced = enhanced.replaceAll("([\\p{IsLatin}\\d])\\s+([\\p{IsHan}])", "$1<break time=\"350ms\" />， $2");

            // 換行符：增加停頓時間到700ms，確保段落分隔更明顯
            enhanced = enhanced.replaceAll("([\\p{IsHan}])\\n+([\\p{IsHan}])", "$1<break time=\"700ms\" />。 $2");
            enhanced = enhanced.replaceAll("([\\p{IsHan}])\\n+", "$1<break time=\"700ms\" />。 ");
            enhanced = enhanced.replaceAll("\\n+([\\p{IsHan}])", "<break time=\"700ms\" />。 $1");
        }

        // 通用換行處理（所有語言）：直接插入break元素
        enhanced = enhanced.replaceAll("\\n+", "<break time=\"500ms\" />。 ");

        // 句號後確保有適當的空格（讓mstts:silence生效）
        enhanced = enhanced.replaceAll("([。.])([^\\s])", "$1 $2");

        // 問號和感嘆號後添加空格
        enhanced = enhanced.replaceAll("([！!？?])([^\\s])", "$1 $2");

        // 逗號和分號後確保有空格（配合mstts:silence設置）
        enhanced = enhanced.replaceAll("([，,；;])([^\\s])", "$1 $2");

        // 冒號後添加空格
        enhanced = enhanced.replaceAll("([：:])([^\\s])", "$1 $2");

        // 省略號和破折號後添加空格
        enhanced = enhanced.replaceAll("([…]+|[-—]{2,})([^\\s])", "$1 $2");

        // 括號內外添加適當空格
        enhanced = enhanced.replaceAll("([^\\s])([（(])", "$1 $2");
        enhanced = enhanced.replaceAll("([）)])([^\\s，,。.！!？?；;：:])", "$1 $2");

        // 引號處理
        enhanced = enhanced.replaceAll("([^\\s])([\"\"''])", "$1 $2");
        enhanced = enhanced.replaceAll("([\"\"''])([^\\s，,。.！!？?；;：:])", "$1 $2");

        // 🔥 多個連續空格處理
        enhanced = enhanced.replaceAll("\\s{2,}", " ");

        // 🔥 多個連續逗號處理
        enhanced = enhanced.replaceAll("，{2,}", "，");

        // 🔥 多個連續句號處理
        enhanced = enhanced.replaceAll("。{2,}", "。");

        return enhanced;
    }
    
    /**
     * 根據文本內容智能決定停頓時間
     * @param currentLine 當前行
     * @param nextLine 下一行
     * @return 停頓時間字符串
     */
    private String determinePauseDuration(String currentLine, String nextLine) {
        // 如果當前行以句號、感嘆號或問號結尾，使用長停頓
        if (currentLine.matches(".*[。.！!？?]\\s*$")) {
            return "600ms";
        }
        
        // 如果下一行以大寫字母開始（新句子），使用中等停頓
        if (nextLine.matches("^\\s*[A-Z].*")) {
            return "450ms";
        }
        
        // 如果是標題或短行（少於10個字符），使用短停頓
        if (currentLine.length() < 10) {
            return "350ms";
        }
        
        // 默認停頓
        return "400ms";
    }
    
    /**
     * 增強數字和日期的朗讀
     */
    private String enhanceNumbersAndDates(String text, String language) {
        String enhanced = text;

        // 根據語言類型處理（使用自然停頓而非say-as標籤，更適合多語言SSML）
        if ("CHINESE".equals(language) || "chinese".equals(language)) {
            // 中文日期處理：添加自然停頓
            enhanced = enhanced.replaceAll("(\\d{4})年(\\d{1,2})月(\\d{1,2})日", "$1年，$2月，$3日");
            enhanced = enhanced.replaceAll("(\\d{4})年(\\d{1,2})月", "$1年，$2月");

            // 中文時間處理
            enhanced = enhanced.replaceAll("(\\d{1,2})點(\\d{2})分", "$1點，$2分");
            enhanced = enhanced.replaceAll("(\\d{1,2}):(\\d{2})", "$1點，$2分");

        } else if ("ENGLISH".equals(language) || "english".equals(language)) {
            // 英文日期處理：添加自然停頓
            enhanced = enhanced.replaceAll("\\b(\\d{4})-(\\d{1,2})-(\\d{1,2})\\b", "$1，$2，$3");
            enhanced = enhanced.replaceAll("\\b(\\d{1,2})/(\\d{1,2})/(\\d{4})\\b", "$1，$2，$3");

            // 英文時間處理
            enhanced = enhanced.replaceAll("\\b(\\d{1,2}):(\\d{2})\\s*(AM|PM|am|pm)\\b", "$1，$2，$3");

        } else if ("WEBLINK".equals(language) || "weblink".equals(language)) {
            // Web鏈接特殊處理：在關鍵分隔符處添加停頓
            enhanced = enhanced.replaceAll("(https?://)([^\\s]+)", "$1，$2");
            enhanced = enhanced.replaceAll("(www\\.)([^\\s]+)", "$1，$2");
            enhanced = enhanced.replaceAll("([^\\s]+)@([^\\s]+)", "$1，at，$2");

        } else if ("NUMBER".equals(language) || "number".equals(language)) {
            // 純數字片段：根據長度添加分隔
            enhanced = enhanced.replaceAll("(\\d{4,})", addNumberSeparators("$1"));

        } else {
            // 通用處理：PUNCTUATION, MIXED, WHITESPACE等
            enhanced = enhanced.replaceAll("(\\d{4})[年/-](\\d{1,2})[月/-](\\d{1,2})[日]?", "$1，$2，$3");
            enhanced = enhanced.replaceAll("(\\d{1,2}):(\\d{2})", "$1，$2");
        }

        // 通用數字處理：添加自然分隔
        // 大數字處理（千位分隔符）
        enhanced = enhanced.replaceAll("\\b(\\d{1,3}),(\\d{3}),(\\d{3})\\b", "$1，$2，$3");
        enhanced = enhanced.replaceAll("\\b(\\d{1,3}),(\\d{3})\\b", "$1，$2");

        // 小數點處理
        enhanced = enhanced.replaceAll("(\\d+)\\.(\\d+)", "$1點$2");

        // 百分比處理
        enhanced = enhanced.replaceAll("(\\d+(?:\\.\\d+)?)%", "$1，percent");

        // 序號處理
        enhanced = enhanced.replaceAll("\\b(\\d+)\\.", "$1，");

        // 貨幣處理（使用自然停頓）
        enhanced = enhanced.replaceAll("\\$([\\d,.]+)", "dollar，$1");
        enhanced = enhanced.replaceAll("￥([\\d,.]+)", "yuan，$1");
        enhanced = enhanced.replaceAll("€([\\d,.]+)", "euro，$1");

        return enhanced;
    }

    /**
     * 為長數字添加分隔符
     */
    private String addNumberSeparators(String number) {
        if (number.length() <= 4) {
            return number;
        }

        // 為長數字每3位添加逗號分隔
        StringBuilder result = new StringBuilder();
        int count = 0;
        for (int i = number.length() - 1; i >= 0; i--) {
            if (count > 0 && count % 3 == 0) {
                result.insert(0, "，");
            }
            result.insert(0, number.charAt(i));
            count++;
        }
        return result.toString();
    }

    /**
     * 🔥 新增：為連續中文字符串添加適當的詞語邊界停頓
     * 這個方法處理長的連續中文字符串，在適當位置添加微停頓，使朗讀更自然
     * @param text 包含中文的文本
     * @return 添加了詞語邊界停頓的文本
     */
    private String addChineseWordBoundaryPauses(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        // 使用正則表達式找到連續的中文字符串（6個字符以上）
        Pattern longChinesePattern = Pattern.compile("([\\p{IsHan}]{6,})");
        Matcher matcher = longChinesePattern.matcher(text);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String longChineseString = matcher.group(1);
            String enhanced = addWordBoundaryPausesToLongString(longChineseString);
            matcher.appendReplacement(result, enhanced);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 🔥 新增：為長中文字符串添加詞語邊界停頓
     * 根據中文詞語的常見長度模式（2-4字詞語）添加適當停頓
     * @param longString 長的中文字符串
     * @return 添加了停頓的字符串
     */
    private String addWordBoundaryPausesToLongString(String longString) {
        if (longString == null || longString.length() < 6) {
            return longString;
        }
        
        StringBuilder enhanced = new StringBuilder();
        int length = longString.length();
        int position = 0;
        
        while (position < length) {
            // 根據中文詞語的常見模式決定分割點
            int segmentLength = determineChineseSegmentLength(longString, position);
            int endPos = Math.min(position + segmentLength, length);
            
            // 添加當前片段
            enhanced.append(longString.substring(position, endPos));
            
            // 🔥 暫時禁用：如果不是最後一個片段，添加詞語邊界停頓
            // 問題：這會在詞語內部添加不必要的停頓，影響自然度
            if (endPos < length) {
                // enhanced.append("<break time=\"250ms\" />");
                Log.d("AzureTTS", String.format("Skipped word boundary pause after: %s",
                    longString.substring(position, endPos)));
            }
            
            position = endPos;
        }
        
        return enhanced.toString();
    }
    
    /**
     * 🔥 新增：智能決定中文字符串的分割長度
     * 基於中文詞語的常見模式（2字詞、3字詞、4字詞居多）
     * @param text 完整的中文字符串
     * @param startPos 當前位置
     * @return 建議的分割長度
     */
    private int determineChineseSegmentLength(String text, int startPos) {
        int remainingLength = text.length() - startPos;
        
        // 如果剩餘長度很短，直接返回
        if (remainingLength <= 3) {
            return remainingLength;
        }
        
        // 根據位置和剩餘長度智能決定分割長度
        // 優先使用2-4字的自然詞語長度
        if (remainingLength >= 8) {
            // 長字符串：交替使用3-4字分割，讓節奏更自然
            return (startPos / 3) % 2 == 0 ? 3 : 4;
        } else if (remainingLength >= 6) {
            // 中等長度：使用3字分割
            return 3;
        } else {
            // 短字符串：使用2字分割
            return 2;
        }
    }

    /**
     * 清理停頓標記，確保格式正確
     */
    private String cleanupPauseMarkers(String text) {
        String cleaned = text;

        // 移除開頭和結尾的停頓標記
        cleaned = cleaned.replaceAll("^[，。\\s]+", "");
        cleaned = cleaned.replaceAll("[，。\\s]+$", "");

        // 合併連續的停頓標記
        cleaned = cleaned.replaceAll("，+", "，");
        cleaned = cleaned.replaceAll("。+", "。");

        // 確保句號後有空格
        cleaned = cleaned.replaceAll("。([^\\s])", "。 $1");

        // 確保逗號後有空格
        cleaned = cleaned.replaceAll("，([^\\s])", "， $1");

        // 清理多餘的空格
        cleaned = cleaned.replaceAll("\\s{2,}", " ");

        // 修復標點符號前的空格
        cleaned = cleaned.replaceAll("\\s+([，。！？：；])", "$1");

        return cleaned.trim();
    }
    
    /**
     * 增強電話號碼朗讀
     */
    private String enhancePhoneNumbers(String text) {
        // 手機號碼格式：13812345678 or 138-1234-5678
        text = text.replaceAll("\\b(1[3-9]\\d)(\\d{4})(\\d{4})\\b", 
            "<say-as interpret-as='telephone'>$1-$2-$3</say-as>");
        
        return text;
    }
    
    /**
     * 增強標點符號的語音效果，包括中英文標點符號
     */
    private String enhancePunctuation(String text) {
        // 句號級停頓（長停頓）- 中英文句號
        text = text.replaceAll("([。.])(?!\\d)", "$1<break time=\"500ms\" />");
        
        // 問號和感嘆號（強調停頓）- 中英文
        text = text.replaceAll("([！!])", "<emphasis level='strong'>$1</emphasis><break time=\"400ms\" />");
        text = text.replaceAll("([？?])", "$1<break time=\"400ms\" />");
        
        // 逗號和分號（中等停頓）- 中英文
        text = text.replaceAll("([，,；;])", "$1<break time=\"300ms\" />");
        
        // 冒號（短停頓）- 中英文
        text = text.replaceAll("([：:])", "$1<break time=\"200ms\" />");
        
        // 省略號和破折號（特殊停頓）
        text = text.replaceAll("([…]+|[-—]{2,})", "$1<break time=\"400ms\" />");
        
        // 🔥 暫時禁用：英文句子結尾後增加停頓（避免誤判中文導致不必要的break）
        // 只在純英文單詞邊界添加停頓，避免影響中文文本
        // text = text.replaceAll("([a-zA-Z]{2,})\\s+([A-Z][a-zA-Z]{2,})", "$1<break time=\"250ms\" /> $2");
        
        return text;
    }
    public void pauseSynthesis() {
        if(!stopstate.get()){
//        synchronized (synchronizedLock) {
                this.isPaused.set(true);
                audioTrack.pause();
//            synchronizedLock.wait(); // Use synchronizedLock to wait
//            }
        }
    }
    public void resumeSynthesis() {
        synchronized (synchronizedLock) {
            if(!stopstate.get()) {
                this.isPaused.set(false);
                synchronizedLock.notifyAll(); // Notify to resume
                audioTrack.play();
            }
        }
    }

    public void stopSynthesis() throws ExecutionException, InterruptedException {
        if (synthesizer != null) {
//            synthesizer.StopSpeakingAsync().get();
            synthesizer.StopSpeakingAsync();
            Log.i("check","stop synthesizer complete");
        }
        if (audioTrack != null) {
            if (audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING || audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PAUSED) {
                Log.i("check","stop AudioTrack!!");
                audioTrack.stop();
                audioTrack.flush();
            }
        }
        synchronized (synchronizedLock) {
            stopstate.set(true);
            isPaused.set(false);
            synchronizedLock.notifyAll();
//            audioTrack.release();
        }
    }

    public void replay(String inputText, float speed) throws ExecutionException, InterruptedException {
        Log.i("check", "entering replaying");
        this.stopSynthesis();
        // startPlaying 方法已經包含SSML處理，無需額外處理
        this.startPlaying(inputText, speed);
    }

    public void destroy() {
        Log.i("AzureTTS", "Destroying AzureTextSynthesis instance");
        
        try {
            // 停止所有合成活動
            if (synthesizer != null) {
                synthesizer.close();
            }
            
            if (speechConfig != null) {
                speechConfig.close();
            }
            
            // 優化的線程池關閉
            if (ttsThread != null && !ttsThread.isShutdown()) {
                ttsThread.shutdown();
                try {
                    if (!ttsThread.awaitTermination(2, java.util.concurrent.TimeUnit.SECONDS)) {
                        ttsThread.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    ttsThread.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            
            // 清理AudioTrack資源
            cleanupAudioTrack();
            
            Log.i("AzureTTS", "AzureTextSynthesis destroyed successfully");
            
        } catch (Exception e) {
            Log.e("AzureTTS", "Error during destruction", e);
        }
    }
    
    /**
     * 清理AudioTrack資源
     */
    private void cleanupAudioTrack() {
        if (audioTrack != null) {
            try {
                // 停止播放
                if (audioTrack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                    audioTrack.stop();
                }
                
                // 清空緩衝區
                audioTrack.flush();
                
                // 釋放AudioTrack資源
                audioTrack.release();
                audioTrack = null;
                
                Log.i("AzureTTS", "AudioTrack resources cleaned up");
                
            } catch (Exception e) {
                Log.e("AzureTTS", "Error cleaning up AudioTrack", e);
            }
        }
    }

    private String mapVoice(String voice){
        switch (voice){
            case "Cantonese":
                return this.voiceSet[0];
            case "Mandarin":
                return this.voiceSet[1];
            case "English":
                return this.voiceSet[2];
        }
        return this.voiceSet[0];
    }

    /**
     * 標準化break元素格式，確保符合Azure TTS SSML規範
     * 🔥 修復：清理break元素中的多餘空格，確保格式嚴格正確
     */
    private String standardizeBreakElements(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        String originalText = text; // 保存原始文本用於比較

        // 修復break元素格式問題：
        // 錯誤格式：<break time= " 250ms " />
        // 正確格式：<break time="250ms"/>

        // 1. 修復time屬性值中的多餘空格
        text = text.replaceAll("<break\\s+time\\s*=\\s*[\"']\\s*([^\"'\\s]+)\\s*[\"']\\s*/?>", "<break time=\"$1\"/>");

        // 2. 確保所有break元素都是自閉合標籤
        text = text.replaceAll("<break\\s+time\\s*=\\s*[\"']([^\"']+)[\"']\\s*>\\s*</break>", "<break time=\"$1\"/>");

        // 3. 統一break元素的空格格式
        text = text.replaceAll("<break\\s+time\\s*=\\s*[\"']([^\"']+)[\"']\\s*/?>", "<break time=\"$1\"/>");

        // 記錄標準化前後的變化
        if (!text.equals(originalText)) {
            Log.d("AzureTTS", "Standardized break elements - changes made");
            Log.v("AzureTTS", "Before: " + originalText.substring(0, Math.min(100, originalText.length())));
            Log.v("AzureTTS", "After: " + text.substring(0, Math.min(100, text.length())));
        } else {
            Log.d("AzureTTS", "Standardized break elements - no changes needed");
        }
        return text;
    }

    /**
     * 驗證和清理SSML文檔，確保符合Azure TTS規範
     * 🔥 修復：最終的SSML格式驗證，防止格式錯誤導致0x80045003錯誤
     */
    private String validateAndCleanSSML(String ssml) {
        if (ssml == null || ssml.isEmpty()) {
            return ssml;
        }

        String cleaned = ssml;

        // 1. 確保break元素格式正確
        cleaned = standardizeBreakElements(cleaned);

        // 2. 移除多餘的空白字符（但保護SSML元素內部格式）
        // 先保護break元素，避免被空白字符處理影響
        Map<String, String> protectedBreaks = new HashMap<>();
        Pattern breakPattern = Pattern.compile("<break\\s+time\\s*=\\s*[\"'][^\"']*[\"']\\s*/?>");
        Matcher breakMatcher = breakPattern.matcher(cleaned);
        int index = 0;

        while (breakMatcher.find()) {
            String placeholder = "___PROTECTED_BREAK_" + (index++) + "___";
            protectedBreaks.put(placeholder, breakMatcher.group());
            cleaned = cleaned.replace(breakMatcher.group(), placeholder);
        }

        // 現在安全地處理空白字符
        cleaned = cleaned.replaceAll("\\s+", " ");

        // 恢復break元素
        for (Map.Entry<String, String> entry : protectedBreaks.entrySet()) {
            cleaned = cleaned.replace(entry.getKey(), entry.getValue());
        }

        // 3. 確保XML聲明格式正確
        cleaned = cleaned.replaceAll("\\s*<\\?xml\\s+version\\s*=\\s*[\"']1\\.0[\"']\\s+encoding\\s*=\\s*[\"']UTF-8[\"']\\s*\\?>\\s*",
                                   "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");

        // 4. 驗證關鍵SSML元素的格式
        if (!cleaned.contains("<speak") || !cleaned.contains("</speak>")) {
            Log.e("AzureTTS", "Invalid SSML: Missing speak element");
            return ssml; // 返回原始內容，讓上層處理
        }

        // 5. 檢查break元素是否有格式問題
        if (cleaned.contains("<break time= ") || cleaned.contains("\" />")) {
            Log.w("AzureTTS", "Found potentially malformed break elements, attempting to fix");
            // 再次標準化
            cleaned = standardizeBreakElements(cleaned);
        }

        Log.d("AzureTTS", "SSML validation and cleanup completed");
        return cleaned;
    }

    /**
     * 基於原始文本空格信息，為中文詞間添加停頓
     * @param text 處理的文本
     * @param originalText 原始文本（包含空格信息）
     * @return 增強停頓的文本
     */
    private String addChineseWordSpacePauses(String text, String originalText) {
        if (text == null || originalText == null || text.isEmpty() || originalText.isEmpty()) {
            return text;
        }

        StringBuilder enhanced = new StringBuilder(text.length() + 200);
        int textIndex = 0;
        int originalIndex = 0;

        // 遍歷原始文本，檢測空格位置
        while (textIndex < text.length() && originalIndex < originalText.length()) {
            char textChar = text.charAt(textIndex);
            char originalChar = originalText.charAt(originalIndex);

            // 如果原始文本中有空格，且前後都是中文字符
            if (originalChar == ' ') {
                // 檢查空格前後是否為中文字符
                boolean prevIsChinese = originalIndex > 0 && isChineseCharacter(originalText.charAt(originalIndex - 1));
                boolean nextIsChinese = originalIndex < originalText.length() - 1 &&
                                      isChineseCharacter(originalText.charAt(originalIndex + 1));

                if (prevIsChinese && nextIsChinese) {
                    // 在中文詞間添加較長停頓（500ms）
                    enhanced.append("<break time=\"500ms\" />，");
                    Log.d("AzureTTS", "Added Chinese word boundary pause at position " + originalIndex);
                } else {
                    // 其他情況添加標準停頓
                    enhanced.append("<break time=\"250ms\" /> ");
                }
                originalIndex++;
                continue;
            }

            // 字符匹配，直接添加
            if (textChar == originalChar) {
                enhanced.append(textChar);
                textIndex++;
                originalIndex++;
            } else {
                // 字符不匹配，可能是文本處理過程中的變化，跳過原始文本字符
                if (originalIndex < originalText.length() - 1) {
                    originalIndex++;
                } else {
                    enhanced.append(textChar);
                    textIndex++;
                }
            }
        }

        // 添加剩餘的文本字符
        while (textIndex < text.length()) {
            enhanced.append(text.charAt(textIndex));
            textIndex++;
        }

        return enhanced.toString();
    }

    /**
     * 檢查字符是否為中文字符
     * @param c 字符
     * @return 是否為中文字符
     */
    private boolean isChineseCharacter(char c) {
        return (c >= 0x4E00 && c <= 0x9FFF) ||           // 基本漢字
               (c >= 0x3400 && c <= 0x4DBF) ||           // 擴展A
               (c >= 0xF900 && c <= 0xFAFF);             // 兼容漢字
    }

    /**
     * 添加語音強調和重音模式，提升自然度
     * @param text 輸入文本
     * @param language 語言類型
     * @return 增強韻律的文本
     */
    private String addProsodyEnhancements(String text, String language) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        String enhanced = text;

        // 1. 為疑問句添加上升語調
        enhanced = enhanced.replaceAll("([^?]*)(\\?)",
            "<prosody pitch=\"+10%\" rate=\"0.9\">$1</prosody>$2");

        // 2. 為感嘆句添加強調
        enhanced = enhanced.replaceAll("([^!]*)(\\!)",
            "<prosody pitch=\"+15%\" rate=\"1.1\" volume=\"+10%\">$1</prosody>$2");

        // 3. 為數字添加清晰發音
        enhanced = enhanced.replaceAll("(\\d+)",
            "<prosody rate=\"0.8\">$1</prosody>");

        // 4. 為重要詞彙添加強調（基於常見模式）
        if ("CHINESE".equals(language) || "chinese".equals(language)) {
            // 中文重要詞彙強調
            enhanced = enhanced.replaceAll("(重要|關鍵|注意|警告|錯誤)",
                "<emphasis level=\"strong\">$1</emphasis>");
        } else if ("ENGLISH".equals(language) || "english".equals(language)) {
            // 英文重要詞彙強調
            enhanced = enhanced.replaceAll("\\b(important|critical|warning|error|attention)\\b",
                "<emphasis level=\"strong\">$1</emphasis>");
        }

        // 5. 為長句子添加自然停頓和語調變化
        enhanced = addNaturalIntonationPatterns(enhanced, language);

        Log.d("AzureTTS", "Applied prosody enhancements for " + language);
        return enhanced;
    }

    /**
     * 添加自然語調模式
     * @param text 輸入文本
     * @param language 語言類型
     * @return 增強語調的文本
     */
    private String addNaturalIntonationPatterns(String text, String language) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        String enhanced = text;

        // 1. 為長句子中間添加輕微語調變化
        if (text.length() > 50) {
            // 在逗號處添加輕微下降語調
            enhanced = enhanced.replaceAll("([^,]{10,})(，|,)([^,]{10,})",
                "$1<prosody pitch=\"-5%\">$2</prosody><break time=\"200ms\" />$3");
        }

        // 2. 為句子結尾添加自然語調
        enhanced = enhanced.replaceAll("([^。.]{20,})(。|\\.)$",
            "$1<prosody pitch=\"-10%\">$2</prosody>");

        // 3. 為並列結構添加語調變化
        enhanced = enhanced.replaceAll("([^、]{5,})(、)([^、]{5,})",
            "$1<prosody pitch=\"+3%\">$2</prosody><break time=\"150ms\" />$3");

        return enhanced;
    }

}
